import {Injectable} from '@angular/core';
import html2canvas from 'html2canvas';
import jspdf from 'jspdf';
import {SelectionsService} from './selections.service';

@Injectable({
    providedIn: 'root'
})
export class PrintService {

    constructor(private selectionsService: SelectionsService) {
    }

    async printElementAsPDF(
        elementSelector: string,
        fileName: string,
        elementsToExpand?: string[]
    ): Promise<void> {

        this.selectionsService.triggerSpinner.emit({show: true, message: 'Generating PDF...'});

        try {
            const element = document.querySelector(elementSelector) as HTMLElement;

            if (!element) {
                throw new Error(`Element with selector '${elementSelector}' not found`);
            }

            // Store original styles and expand modal elements
            const elementsToModify = this.getElementsToExpand(element, elementsToExpand);
            const originalStyles = new Map();

            this.storeAndModifyStyles(elementsToModify, originalStyles);

            // Wait for DOM to adjust and any remaining animations
            await this.delay(2000);

            // Take screenshot and generate PDF
            const canvas = await html2canvas(element, {
                useCORS: true,
                allowTaint: false,
                scale: 1,
                backgroundColor: '#ffffff',
                height: element.scrollHeight,
                width: element.scrollWidth,
                scrollX: 0,
                scrollY: 0
            });

            // Restore styles
            this.restoreStyles(originalStyles);

            // Note: Accordion restoration would need to be handled by the calling component

            // Generate and save PDF
            await this.generatePDF(canvas, fileName);

            // Hide loading spinner
            this.selectionsService.triggerSpinner.emit({show: false});

        } catch (error) {
            // Hide loading spinner even on error
            this.selectionsService.triggerSpinner.emit({show: false});

            console.error('Print failed:', error);
            throw error;
        }
    }


    private getElementsToExpand(element: HTMLElement, customElements?: string[]): HTMLElement[] {
        let elementsToFind: string[] = [];

        if (customElements && customElements.length > 0) {
            elementsToFind = customElements;
        }

        // Get elements from selectors
        const selectedElements = elementsToFind
            .map(selector => selector.startsWith('#')
                ? document.getElementById(selector.substring(1))
                : document.querySelector(selector)
            )
            .filter(el => el) as HTMLElement[];

        // Add any elements with scroll overflow
        const scrollableElements = Array.from(element.querySelectorAll('*')).filter((el: HTMLElement) => {
            const computedStyle = window.getComputedStyle(el);
            return computedStyle.overflow === 'scroll' || computedStyle.overflow === 'auto' ||
                computedStyle.overflowY === 'scroll' || computedStyle.overflowY === 'auto' ||
                computedStyle.overflowX === 'scroll' || computedStyle.overflowX === 'auto'
        }) as HTMLElement[];

        return [...selectedElements, ...scrollableElements];
    }

    private storeAndModifyStyles(elements: HTMLElement[], originalStyles: Map<HTMLElement, any>): void {
        elements.forEach((el: HTMLElement) => {
            if (el) {
                originalStyles.set(el, {
                    height: el.style.height,
                    maxHeight: el.style.maxHeight,
                    minHeight: el.style.minHeight,
                    overflow: el.style.overflow,
                    overflowY: el.style.overflowY,
                    overflowX: el.style.overflowX
                });

                // Expand all elements to show full content
                el.style.height = 'auto';
                el.style.maxHeight = 'none';
                el.style.minHeight = 'auto';
                el.style.overflow = 'visible';
                el.style.overflowY = 'visible';
                el.style.overflowX = 'visible';
            }
        });
    }

    private restoreStyles(originalStyles: Map<HTMLElement, any>): void {
        originalStyles.forEach((styles, el) => {
            Object.keys(styles).forEach(prop => {
                el.style[prop] = styles[prop];
            });
        });
    }

    private async generatePDF(canvas: HTMLCanvasElement, fileName: string): Promise<void> {
        const imgData = canvas.toDataURL('image/png');
        const imgWidth = canvas.width;
        const imgHeight = canvas.height;

        // Calculate PDF dimensions (A4 or custom size based on content)
        const pdfWidth = imgWidth > imgHeight ? 297 : 210; // A4 landscape or portrait
        const pdfHeight = (imgHeight * pdfWidth) / imgWidth;

        const pdf = new jspdf(imgWidth > imgHeight ? 'l' : 'p', 'mm', [pdfWidth, pdfHeight]);
        pdf.addImage(imgData, 'PNG', 0, 0, pdfWidth, pdfHeight);
        pdf.save(`${fileName}.pdf`);
    }

    private delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}
