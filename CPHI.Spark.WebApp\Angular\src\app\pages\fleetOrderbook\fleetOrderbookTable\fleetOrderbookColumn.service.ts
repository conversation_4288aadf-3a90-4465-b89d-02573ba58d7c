import { EventEmitter, Injectable } from "@angular/core";
import { CellClassFunc, CellClassParams, ColDef, ValueGetterParams } from "ag-grid-community";
import { FleetOrderbookRow } from "src/app/model/FleetOrderbookRow";
import { AGGridMethodsService } from "src/app/services/agGridMethods.service";
import { FleetOrderbookCommentsCellComponent } from "../fleetOrderbookCommentsCell/fleetOrderbookCommentsCell.component";
import { CPHAutoPriceColDef } from "src/app/model/CPHColDef";
import { CdkRegCustomFilter } from "./CdkRegCustomFilter.component";

@Injectable({
  providedIn: 'root'
})
export class FleetOrderbookColumnService {

  public closeCommentEmitter: EventEmitter<boolean> = new EventEmitter()

  constructor(
    private gridHelpersService: AGGridMethodsService
  ) {

  }



  public provideNissanColDefs(rowData: FleetOrderbookRow[]): CPHAutoPriceColDef[] {


    let colDefs: CPHAutoPriceColDef[] = [
      { headerName: 'Veh Order No.', colId: 'VehicleOrderNumber', field: 'VehicleOrderNumber', width: 20, type: 'labelSetFilter', columnSection: 'Fleet Order book' },
      { headerName: 'VOrderNo.', colId: 'VehicleOrderNumberNoLeadingLetters', valueGetter: params => this.vordNoGetter(params), field: 'VehicleOrderNumber', width: 20, 
      type: 'number',columnSection: 'Fleet Order book' },
      { headerName: 'Order Date', colId: 'OrderDate', field: 'OrderDate', type: 'dateLongYear', editable: false,columnSection: 'Fleet Order book' },
      { headerName: 'Owning Main Dealer', colId: 'OwningMainDealer', field: 'OwningMainDealer', valueGetter: (params) => params.data ? params.data['OwningMainDealer']?.toString() : '', 
      type: 'labelSetFilter',columnSection: 'Fleet Order book' },
      { headerName: 'StockNumber', headerComponentParams: { menuIcon: "fa-bars" }, colId: 'StockNumber', field: 'StockNumber', type: 'labelSetFilter', width: 10 ,columnSection: 'Fleet Order book'},
      //{ headerName: 'Stock Category', colId: 'StockCategory', field: 'StockCategory', type: 'stockCategory', },  
      { headerName: 'Sales Person', colId: 'SalesPerson', field: 'SalesPerson', type: 'labelEditable', cellClass: ['agAlignLeft', 'green'], columnSection: 'Fleet Order book'},
      { headerName: 'End User', colId: 'EndUser', field: 'EndUser', type: 'labelEditable', cellClass: ['agAlignLeft', 'green'], columnSection: 'Fleet Order book'},
      { headerName: 'FON Code', colId: 'FonCode', field: 'FonCode', type: 'labelEditable', cellClass: ['agAlignLeft', 'green'], columnSection: 'Fleet Order book'},
      { headerName: 'Cust Order No.', colId: 'CustomerOrderNo', field: 'CustomerOrderNo', type: 'numberEditableNoCommas', cellClass: ['ag-right-aligned-cell', 'green'], columnSection: 'Fleet Order book'},
      { headerName: 'Del Depot', colId: 'DeliveryDepot', field: 'DeliveryDepot', type: 'delDepot', columnSection: 'Fleet Order book'},
      { headerName: 'On Latest Download?', colId: 'IsOnLatestDownload', field: 'IsOnLatestDownload', type: 'booleanWithFloatingFilter',columnSection: 'Fleet Order book' },
      { headerName: 'Registered Keeper', colId: 'Customer', field: 'Customer', type: 'labelSetFilter' ,columnSection: 'Fleet Order book'},
      { headerName: 'Model', colId: 'Model', field: 'Model', type: 'labelSetFilter', columnSection: 'Fleet Order book'},
      { headerName: 'Version', colId: 'Version', field: 'Version', type: 'labelSetFilter', columnSection: 'Fleet Order book'},
      { headerName: 'Chassis', colId: 'Chassis', field: 'Chassis', type: 'labelSetFilter', width: 10 ,columnSection: 'Fleet Order book'},
      { headerName: 'Drop Code', colId: 'DropCode', field: 'DropCode', type: 'labelSetFilter',columnSection: 'Fleet Order book' },
      { headerName: 'Due ATIC', colId: 'DueATIC', field: 'DueATIC', type: 'dateLongYear',columnSection: 'Fleet Order book' },
      { headerName: 'Status', colId: 'Status', field: 'Status', type: 'labelSetFilter', columnSection: 'Fleet Order book'},
      { headerName: 'Stock Age', colId: 'TrueAge', field: 'TrueAge', type: 'number', filter: 'agNumberColumnFilter' ,columnSection: 'Fleet Order book'},
      { headerName: 'Reg', colId: 'Reg', field: 'Reg', type: 'labelEditable', cellClass: ['agAlignLeft', 'green'], columnSection: 'Fleet Order book'},
      { headerName: 'CDK Reg', colId: 'DealsReg', field: 'DealsReg', type: 'labelSetFilter', columnSection: 'Fleet Order book'},
      { headerName: 'Driver Pk Req', colId: 'DriverPackRequired', field: 'DriverPackRequired', type: 'booleanEditable', columnSection: 'Fleet Order book'},
      { headerName: 'Driver Pk Ord', colId: 'DriverPackOrdered', field: 'DriverPackOrdered', type: 'booleanEditable', columnSection: 'Fleet Order book'},
      { headerName: 'PIVG Reference', colId: 'PivgReference', field: 'PivgReference', type: 'labelEditable', cellClass: ['agAlignLeft', 'green'], columnSection: 'Fleet Order book'},
      { headerName: 'Delivery Agent', colId: 'DeliveryAgent', field: 'DeliveryAgent', type: 'labelEditable', cellClass: ['agAlignLeft', 'green'], columnSection: 'Fleet Order book'},
      { headerName: 'Delivery Date', colId: 'DeliveryDate', field: 'DeliveryDate', type: 'dateEditable', cellClass: ['agAlignCentre', 'green'], editable: true, columnSection: 'Fleet Order book'},
      { headerName: 'Del Month', colId: 'DeliveryMonth', field: 'DeliveryMonth', type: 'labelSetFilter', columnSection: 'Fleet Order book'},
      { headerName: 'Removed Date', colId: 'RemovedDate', field: 'RemovedDate', type: 'dateLongYear', columnSection: 'Fleet Order book'},
      { headerName: 'Invoice Date', colId: 'InvoiceDateToCustomer', field: 'InvoiceDateToCustomer', type: 'dateLongYear',columnSection: 'Fleet Order book' },  //ok
      { headerName: 'Invoiced In Month?', colId: 'IsInvoicedInMonth', field: 'IsInvoicedInMonth', type: 'booleanWithFloatingFilter',columnSection: 'Fleet Order book' },  //ok
      { headerName: 'Hidden?', colId: 'IsHidden', field: 'IsHidden', type: 'booleanWithFloatingFilter', columnSection: 'Fleet Order book'},

      { headerName: 'Inventory Type', colId: 'InventoryType', field: 'InventoryType', type: 'labelSetFilter', columnSection: 'Fleet Order book'},  //should be InventoryType
      { headerName: 'Ebbon Status', colId: 'EbbonStatus', field: 'EbbonStatus', type: 'labelSetFilter' ,columnSection: 'Fleet Order book'},
      { headerName: 'Ebbon Stage', colId: 'EbbonStage', field: 'EbbonStage', type: 'labelSetFilter' ,columnSection: 'Fleet Order book'},
      {
        headerName: 'Comments', pinned: 'right', colId: 'SparkComments', cellRenderer: FleetOrderbookCommentsCellComponent, cellRendererParams: { closePopupEmitter: this.closeCommentEmitter },
        field: 'SparkComments', width: 100, filter: 'agTextColumnFilter', type: 'labelSetFilter',columnSection: 'Fleet Order book'
      },
      { headerName: 'Count', colId: 'Count', valueGetter: (params) => 1, type: 'number',columnSection: 'Fleet Order book' }

    ] as CPHAutoPriceColDef[];

    //now workout column widths
    this.gridHelpersService.workoutColWidths(rowData, colDefs, 10, 8);
    //manually set some widths back again
    colDefs.find(x => x.colId == 'SparkComments').width = 200;
    colDefs.find(x => x.colId == 'OrderDate').width = 140;
    colDefs.find(x => x.colId == 'DueATIC').width = 140;

    //colDefs.find(x => x.colId == 'Options').width = 500; This column is no longer here?
    //colDefs.find(x => x.colId == 'LabelOptions').width = 500; This column is no longer here?

    colDefs.forEach(def => {
      if (def.type === 'labelWithSetFilter' || def.type === 'labelLowPadIdWithSetFilter') {
        def.filterParams = { clearButton: true, filterOptions: this.provideTextColFilters(rowData, def.field) }
      }
    })

    //console.log(colDefs)
    return colDefs;
  }
  vordNoGetter(params: ValueGetterParams): any {
    const row: FleetOrderbookRow = params.data;
    if (!row) { return '' }
    return row.VehicleOrderNumber?.replace('EGB', '');
  }



  public provideRenaultColDefs(rowData: FleetOrderbookRow[]): CPHAutoPriceColDef[] {


    let colDefs: CPHAutoPriceColDef[] = [
      { headerName: 'Cust Order No.', colId: 'CustomerOrderNo', field: 'CustomerOrderNo', type: 'labelSetFilter', columnSection: 'Fleet Order book' },
      { headerName: 'Chassis', colId: 'Chassis', field: 'Chassis', type: 'labelSetFilter', width: 10, columnSection: 'Fleet Order book' },
      { headerName: 'StockNumber', colId: 'StockNumber', field: 'StockNumber', type: 'labelSetFilter', width: 10, columnSection: 'Fleet Order book' },
      { headerName: 'Stock Category', colId: 'StockCategory', field: 'StockCategory', type: 'stockCategory', columnSection: 'Fleet Order book' },
      { headerName: 'Veh Order No.', colId: 'VehicleOrderNumber', field: 'VehicleOrderNumber', type: 'labelSetFilter', columnSection: 'Fleet Order book' },
      { headerName: 'Lease Co Order Number', colId: 'LeaseNumber', field: 'LeaseNumber', type: 'labelSetFilter', columnSection: 'Fleet Order book' },
      {
        headerName: 'Owning Main Dealer', colId: 'OwningMainDealer',
        valueGetter: (params) => params.data ? params.data['OwningMainDealer']?.toString() : '', field: 'OwningMainDealer', type: 'labelSetFilter', columnSection: 'Fleet Order book'
      },
      { headerName: 'Fon Code', colId: 'FonCode', field: 'FonCode', type: 'labelSetFilter', columnSection: 'Fleet Order book' },
      { headerName: 'Fon Description', colId: 'FonDescription', field: 'FonDescription', type: 'labelSetFilter', columnGroupShow: 'open', columnSection: 'Fleet Order book' },

      { headerName: 'Converter Name', colId: 'ConverterName', field: 'ConverterName', type: 'labelEditable', cellClass: ['agAlignLeft', 'green'], columnSection: 'Fleet Order book' },
      { headerName: 'Acc Handler', colId: 'AccountHandlerName', field: 'AccountHandlerName', type: 'labelEditable', cellClass: ['agAlignLeft', 'green'], columnSection: 'Fleet Order book' },
      //{ headerName: 'Conv. Bld', colId: 'ConvBldDte', field: 'ConvBldDte', type: 'dateEditable', },
      { headerName: 'Conv. Bld Start Date', colId: 'ConvBldDte', field: 'ConvBldDte', type: 'dateEditable', columnSection: 'Fleet Order book' },
      { headerName: 'Conv. Est. Compl', colId: 'ConvEstComplDte', field: 'ConvEstComplDte', type: 'dateEditable', columnSection: 'Fleet Order book' },

      { headerName: 'Status', colId: 'Status', field: 'Status', type: 'labelSetFilter', columnSection: 'Fleet Order book' },
      { headerName: 'FRD', colId: 'FRD', field: 'FRD', type: 'dateLongYear', columnSection: 'Fleet Order book' },
      { headerName: 'PROM ATIC', colId: 'PromAtic', field: 'PromAtic', type: 'dateLongYear', columnSection: 'Fleet Order book' },
      { headerName: 'Probable ATIC', colId: 'ProbableAtic', field: 'ProbableAtic', type: 'dateLongYear', columnSection: 'Fleet Order book' },
      { headerName: 'Actual ATIC', colId: 'ActualAtic', field: 'ActualAtic', type: 'dateLongYear', columnSection: 'Fleet Order book' },
      { headerName: 'True Age', colId: 'TrueAge', field: 'TrueAge', type: 'number', columnSection: 'Fleet Order book' },
      { headerName: 'GFC Stock Age', colId: 'GFCStockAge', field: 'GFCStockAge', type: 'number', columnSection: 'Fleet Order book' },
      { headerName: 'Customer', colId: 'Customer', field: 'Customer', type: 'labelSetFilter', columnSection: 'Fleet Order book' },
      { headerName: 'SalesExecPin', colId: 'SalesExecPin', field: 'SalesExecPin', type: 'labelSetFilter', columnSection: 'Fleet Order book' },
      { headerName: 'On Latest Download?', colId: 'IsOnLatestDownload', field: 'IsOnLatestDownload', type: 'booleanWithFloatingFilter', columnSection: 'Fleet Order book' },

      { headerName: 'Accessory Name', colId: 'PlyAccName', field: 'PlyAccName', type: 'labelSetFilter', columnSection: 'Fleet Order book' },
      { headerName: 'Dealer Cost', colId: 'PlyDealerCost', field: 'PlyDealerCost', type: 'currencySetFilter', columnSection: 'Fleet Order book' },
      { headerName: 'Ply Order Date', colId: 'PlyOrderDate', field: 'PlyOrderDate', type: 'dateLongYear', columnSection: 'Fleet Order book' },
      { headerName: 'Ply Fitment Date', colId: 'PlyFitmentDate', field: 'PlyFitmentDate', type: 'dateLongYear', columnSection: 'Fleet Order book' },
      { headerName: 'Ply Completion Date', colId: 'PlyComplDate', field: 'PlyComplDate', type: 'labelSetFilter', columnSection: 'Fleet Order book' },

      { headerName: 'Cust Order Date', colId: 'OrderDate', field: 'OrderDate', type: 'dateLongYear', columnSection: 'Fleet Order book' },
      { headerName: 'Act Matched Date', colId: 'ActualMatchedDate', field: 'ActualMatchedDate', type: 'dateLongYear', columnSection: 'Fleet Order book' }, //ok to here
      { headerName: 'Matched In Month?', colId: 'IsMatchedInMonth', field: 'IsMatchedInMonth', type: 'booleanWithFloatingFilter', columnSection: 'Fleet Order book' }, //ok to here
      { headerName: 'Sales Channel', colId: 'SalesChannel', field: 'SalesChannel', type: 'labelEditable', columnSection: 'Fleet Order book' },  //ok
      { headerName: 'Drop Code', colId: 'DropCode', field: 'DropCode', type: 'labelSetFilter', columnSection: 'Fleet Order book' },  //ok
      { headerName: 'Age At EOM', colId: 'AgeAtEom', field: 'AgeAtEom', type: 'number', columnSection: 'Fleet Order book' }, // ok 
      { headerName: 'Driver Pk Req', colId: 'DriverPackRequired', field: 'DriverPackRequired', type: 'booleanEditable', columnSection: 'Fleet Order book' },
      { headerName: 'Driver Pk Ord', colId: 'DriverPackOrdered', field: 'DriverPackOrdered', type: 'booleanEditable', columnSection: 'Fleet Order book' },
      { headerName: 'PIVG Reference', colId: 'PivgReference', field: 'PivgReference', type: 'labelEditable', cellClass: ['agAlignLeft', 'green'], columnSection: 'Fleet Order book'},
      { headerName: 'Delivery Agent', colId: 'DeliveryAgent', field: 'DeliveryAgent', type: 'labelEditable', cellClass: ['agAlignLeft', 'green'], columnSection: 'Fleet Order book'},
      { headerName: 'Del due not regd', colId: 'delDueNotRegd', field: 'delDueNotRegd', type: 'boolean', columnSection: 'Fleet Order book' },
      { headerName: 'Delivery Date', colId: 'DeliveryDate', field: 'DeliveryDate', type: 'dateEditable', cellClass: ['agAlignCentre', 'green'], editable: true, columnSection: 'Fleet Order book' },  //ok
      { headerName: 'Del Month', colId: 'DeliveryMonth', field: 'DeliveryMonth', type: 'labelSetFilter', columnSection: 'Fleet Order book' },
      { headerName: 'Removed Date', colId: 'RemovedDate', field: 'RemovedDate', type: 'dateLongYear', columnSection: 'Fleet Order book' },
      { headerName: 'Reg', colId: 'Reg', field: 'Reg', type: 'labelSetFilter', cellClass: ['agAlignLeft', 'green'], columnSection: 'Fleet Order book' },
      { headerName: 'Report Reg', colId: 'ReportReg', field: 'ReportReg', type: 'labelSetFilter', columnSection: 'Fleet Order book' },   //ok
      { headerName: 'CDK Reg', colId: 'DealsReg', field: 'DealsReg', cellClass: (params) => this.dealsReg(params), type: 'cdkFilter', filter: CdkRegCustomFilter, columnSection: 'Fleet Order book' },   //ok
      { headerName: 'Reg mismatch?', colId: 'RegMismatch', field: 'regMismatch', type: 'booleanWithFloatingFilter', columnSection: 'Fleet Order book' },   //ok
      { headerName: 'DDD Date', colId: 'DDDDate', field: 'DDDDate', type: 'dateLongYear', columnSection: 'Fleet Order book' },  //ok
      { headerName: 'Reg Date', colId: 'RegDate', field: 'RegistrationDate', type: 'dateLongYear', columnSection: 'Fleet Order book' },  //ok
      { headerName: 'Registered In Month?', colId: 'IsRegisteredInMonth', field: 'IsRegisteredInMonth', type: 'boolean', columnSection: 'Fleet Order book' },  //ok
      { headerName: 'Model', colId: 'Model', field: 'Model', type: 'labelEditable', columnSection: 'Fleet Order book' },   //ok
      { headerName: 'Version', colId: 'Version', field: 'Version', type: 'labelEditable', columnSection: 'Fleet Order book' },  //ok
      { headerName: 'Colour', colId: 'Colour', field: 'Colour', type: 'labelEditable', columnSection: 'Fleet Order book' }, //ok
      { headerName: 'Options', colId: 'Options', field: 'Options', type: 'labelEditable', columnSection: 'Fleet Order book' }, //ok
      { headerName: 'Label Options', colId: 'LabelOptions', field: 'LabelOptions', type: 'labelEditable', columnSection: 'Fleet Order book' }, //ok
      { headerName: 'Invoice Date', colId: 'InvoiceDateToCustomer', field: 'InvoiceDateToCustomer', type: 'dateLongYear', columnSection: 'Fleet Order book' },  //ok
      { headerName: 'Invoiced In Month?', colId: 'IsInvoicedInMonth', field: 'IsInvoicedInMonth', type: 'boolean', columnSection: 'Fleet Order book' },  //ok

      { headerName: 'Profit In Deal', colId: 'TotalNLProfit', field: 'TotalNLProfit', type: 'currencySetFilter', columnSection: 'Fleet Order book' }, //ok
      { headerName: 'BCA Status', colId: 'BcaStatus', field: 'BcaStatus', type: 'labelSetFilter', columnSection: 'Fleet Order book' },//ok
      { headerName: 'BCA Arrival', colId: 'BcaArrivalDate', field: 'BcaArrivalDate', type: 'dateLongYear', columnSection: 'Fleet Order book' },//ok
      { headerName: 'BCA DDD', colId: 'BcaDdd', field: 'BcaDdd', type: 'dateLongYear', columnSection: 'Fleet Order book' },//ok
      { headerName: 'BCA Est Arrival', colId: 'BcaEstArrivalDate', field: 'BcaEstArrivalDate', type: 'dateLongYear', columnSection: 'Fleet Order book' },//ok
      { headerName: 'Campaign', colId: 'Campaign', field: 'Campaign', type: 'bcaDamage', columnSection: 'Fleet Order book' },//ok
      { headerName: 'Damage', colId: 'Damage', field: 'Damage', type: 'bcaDamage', columnSection: 'Fleet Order book' },//ok
      { headerName: 'Estimate', colId: 'Estimate', field: 'Estimate', type: 'bcaDamage', columnSection: 'Fleet Order book' },//ok
      { headerName: 'Mechanical', colId: 'Mechanical', field: 'Mechanical', type: 'bcaDamage', columnSection: 'Fleet Order book' },//ok

      { headerName: 'Ebbon Status', colId: 'EbbonStatus', field: 'EbbonStatus', type: 'labelEditable', columnSection: 'Fleet Order book' },
      { headerName: 'Ebbon Stage', colId: 'EbbonStage', field: 'EbbonStage', type: 'labelEditable', columnSection: 'Fleet Order book' },

      { headerName: 'Ord Comment', colId: 'Comment', field: 'Comment', type: 'labelSetFilter', columnSection: 'Fleet Order book' },//ok
      { headerName: 'Ord Comments', colId: 'OrderComments', field: 'OrderComments', type: 'labelSetFilter', columnSection: 'Fleet Order book' },//ok
      { headerName: 'FullConversionAgeEoM', colId: 'FullConversionAgeEoM', field: 'FullConversionAgeEoM', type: 'labelSetFilter', columnSection: 'Fleet Order book' },//ok
      { headerName: 'CarPersonalisationAgeEoM', colId: 'CarPersonalisationAgeEoM', field: 'CarPersonalisationAgeEoM', type: 'labelSetFilter', columnSection: 'Fleet Order book' },//ok


      //SPK-4313 4 more cols
      {
        headerName: 'Forecast Is Going', colId: 'RUKIsGoing', field: 'RUKIsGoing', type: 'labelEditable', editable: true, cellEditor: 'agRichSelectCellEditor', cellEditorPopup: true,
        cellEditorParams: { cellHeight: 30, values: ['Going', 'Not Going'] }, columnSection: 'Fleet Order book'
      },

      {
        headerName: 'Forecast Reason', colId: 'RUKForecastReason', field: 'RUKForecastReason', type: 'labelEditable', cellClass: (params) => this.forecastClass(params),
        editable: true, cellEditor: 'agRichSelectCellEditor', cellEditorPopup: true, cellEditorParams: {
          cellHeight: 30, values: [
            'Cancelled order / stock',
            'Customer delivery in future month',
            'Damaged / awaiting parts / technical block',
            'Delayed',
            'Fleet Sale, no end customer',
            'Sold add. Works / conversion required',
            'Sold conversion required',
            `Sold, customer can't take delivery`,
            'Unsold',
            'Unsold, add. Works / conversion required',
            'Unsold, conversion required',
          ], columnSection: 'Fleet Order book'
        }
      },
      {
        headerName: 'Forecast Sub-Reason', cellClass: (params) => this.forecastClass(params), colId: 'RUKForecastSubReason', field: 'RUKForecastSubReason', type: 'labelEditable',
        editable: true, cellEditor: 'agRichSelectCellEditor', cellEditorPopup: true, cellEditorParams: (params) => { return { cellHeight: 30, values: this.calculateRelevantSubReasons(params) } },
        columnSection: 'Fleet Order book'

      },
      {
        headerName: 'Forecast FRD', colId: 'RUKForecastFRD', cellClass: (params) => this.forecastClass(params), field: 'RUKForecastFRD',
        type: 'dateEditable', editable: true, columnSection: 'Fleet Order book'
      },  //ok


      {
        headerName: 'Comments', pinned: 'right', colId: 'SparkComments', cellRenderer: FleetOrderbookCommentsCellComponent, cellRendererParams: { closePopupEmitter: this.closeCommentEmitter },
        field: 'SparkComments', width: 100, type: 'labelSetFilter', valueGetter: (params) => this.commentsFilterValueGetter(params), columnSection: 'Fleet Order book'
      },




    ] as CPHAutoPriceColDef[];

    //now workout column widths

    this.gridHelpersService.workoutColWidths(rowData, colDefs, 10, 8);
    colDefs.find(x => x.colId == 'FRD').width = 140;
    colDefs.find(x => x.colId == 'OrderDate').width = 140;
    colDefs.find(x => x.colId == 'PromAtic').width = 140;
    colDefs.find(x => x.colId == 'ProbableAtic').width = 140;
    colDefs.find(x => x.colId == 'ActualAtic').width = 140;
    colDefs.find(x => x.colId == 'DeliveryDate').width = 140;
    colDefs.find(x => x.colId == 'DDDDate').width = 140;
    colDefs.find(x => x.colId == 'RegDate').width = 140;
    colDefs.find(x => x.colId == 'InvoiceDateToCustomer').width = 140;
    colDefs.find(x => x.colId == 'SparkComments').width = 200;
    colDefs.find(x => x.colId == 'RegMismatch').width = 10;
    colDefs.find(x => x.colId == 'Options').width = 500;
    colDefs.find(x => x.colId == 'LabelOptions').width = 500;
    colDefs.find(x => x.colId == 'RUKForecastFRD').width = 140;
    colDefs.find(x => x.colId == 'RUKForecastSubReason').width = 200;


    colDefs.forEach(def => {
      if (def.type === 'labelWithSetFilter' || def.type === 'labelLowPadIdWithSetFilter') {
        def.filterParams = { clearButton: true, filterOptions: this.provideTextColFilters(rowData, def.field) }
      }
    })

    return colDefs;
  }
  forecastClass(params): string | string[] | CellClassFunc<any> {
    let classes = ['agAlignLeft', 'green']
    const row: FleetOrderbookRow = params.data;


    if (params.colDef.colId == 'RUKForecastReason' && row.missingReason) {
      classes.push('bad')
    }
    else if (params.colDef.colId == 'RUKForecastSubReason' && row.missingSubReason) {
      classes.push('bad')
    }
    else if (params.colDef.colId == 'RUKForecastFRD' && row.missingFRD) {
      classes.push('bad')
    }

    return classes;

  }
  calculateRelevantSubReasons(params: any) {
    const row: FleetOrderbookRow = params.data;
    console.log(params);

    let reasonsNeedingWeeksSubReasons = [
      'Sold add. Works / conversion required',
      'Sold conversion required',
      'Unsold, add. Works / conversion required',
      'Unsold, conversion required'
    ]

    let weeksSubReasons = [
      '2 week conversion lead time',
      '4 week conversion lead time',
      '8 week conversion lead time',
      '12+ week conversion lead time',
    ]

    const regsSubReasons = [
      'Registration M+1',
      'Registration M+2',
      'Registration M+3',
    ]

    if (!row.RUKForecastReason) { return [] }
    return reasonsNeedingWeeksSubReasons.includes(row.RUKForecastReason) ? weeksSubReasons : regsSubReasons




  }

  commentsFilterValueGetter(params): string {
    let commentsString: string = params.data.SparkComments;

    if (commentsString === '') { return null; }

    const commentsArray: string[] = commentsString.split('^#^');
    // Comment looks like "6005|2023-11-08 18:17:10.5877893|PURGED COF - DAYS RENTAL|Christopher Lever"
    // Return all comment messages joined with a pipe
    const res = commentsArray.map(x => x.split('|')[2]).join(' | ');
    return res;
  }

  dealsReg(params: CellClassParams): string | string[] {
    const row: FleetOrderbookRow = params.data;
    if (row) { return '' }
    if (this.isMismatchReg(row)) {
      return 'redCell'
    }
  }

  isMismatchReg(row: FleetOrderbookRow): boolean {
    return row.regMismatch
  }

  // custOrdNoGetter(params: ValueGetterParams): string {
  //   const row: FleetOrderbookRow = params.data;
  //   if(!row){return ''}
  //   return row.CustomerOrderNo.toString().padStart(6, '0');
  // }



  provideTextColFilters(rowData: FleetOrderbookRow[], field: string) {

    var results = this.normalTextFilters();
    results.push(
      {
        displayKey: '------------------',
        displayName: '-------------------',
        test: function (filterValue, cellValue) {
          return true;
        },
        hideFilterInput: true,
      }

    )

    const uniqueValues = [...new Set(rowData.map(item => item[field]))];
    uniqueValues.forEach(fieldValue => {
      if (fieldValue) {

        results.push({
          displayKey: fieldValue,
          displayName: fieldValue,
          test: function (filterValue, cellValue) {
            return cellValue && typeof (cellValue) === 'string' && cellValue !== undefined && cellValue !== '' && cellValue.toUpperCase() === fieldValue.toUpperCase();
          },
          hideFilterInput: true,
        })
      }
    })

    return results;
  }




  normalTextFilters() {
    return ['contains',
      'notContains',
      {
        displayKey: 'blanks',
        displayName: 'Blanks',
        test: function (filterValue, cellValue) {
          return cellValue === undefined || cellValue === '';
        },
        hideFilterInput: true,
      },
      {
        displayKey: 'notBlanks',
        displayName: 'Not Blanks',
        test: function (filterValue, cellValue) {
          return cellValue !== undefined && cellValue !== '';
        },
        hideFilterInput: true,
      },
      'equals',
      'notEqual',
      'startsWith',
      'endsWith',
      //'lessThan',
      //'lessThanOrEqual',
      //'greaterThan',
      //'greaterThanOrEqual',
      //'inRange'

    ]
  }

  normalBooleanOptions() {
    return [
      {
        displayKey: 'True',
        displayName: 'True',
        test: function (filterValue, cellValue) {
          return cellValue;
        },
        hideFilterInput: true,
      },
      {
        displayKey: 'False',
        displayName: 'False',
        test: function (filterValue, cellValue) {
          return cellValue !== undefined && cellValue !== null && !cellValue;
        },
        hideFilterInput: true,
      },
    ]
  }

}