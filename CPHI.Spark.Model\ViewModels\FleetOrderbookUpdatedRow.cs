﻿using System;

namespace CPHI.Spark.Model.ViewModels
{
   public class FleetOrderbookUpdatedRow
   {
      public int RowId { get; set; }
      //the updated properties

      public string SalesPerson { get; set; }
      public string EndUser { get; set; }
      public string CustomerOrderNo { get; set; }
      public string DeliveryDepot { get; set; }
      public DateTime? InvoiceDate { get; set; }
      //public string FonDescription { get; set; }
      public string FonCode { get; set; }
      public string AlarmData { get; set; }
      public string StockCategory { get; set; }

      public string Reg { get; set; }


      public bool? DriverPackRequired { get; set; }
      public bool? DriverPackOrdered { get; set; }
      public DateTime? DeliveryDate { get; set; }

      public string AccountHandlerName { get; set; }
      public DateTime? ConvBldDte { get; set; }
      public DateTime? ConvEstComplDte { get; set; }
      public string ConverterName { get; set; }
      public DateTime? ConverterBuildStartDate { get; set; }


      public bool? IsH<PERSON>den { get; set; }
      public bool IsRemoved { get; set; }
      public bool ClearDeliveryDate { get; set; }


      public string RUKIsGoing { get; set; }
      public string RUKForecastReason { get; set; }
      public string RUKForecastSubReason { get; set; }
      public DateTime? RUKForecastFRD { get; set; }


      // New properties added - 1st Sept 25
      public string PivgReference { get; set; }
      public string DeliveryAgent { get; set; }
   }
}
