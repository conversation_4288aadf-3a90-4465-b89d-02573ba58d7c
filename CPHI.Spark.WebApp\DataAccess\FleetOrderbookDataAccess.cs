﻿using CPHI.Repository;
using CPHI.Spark.DataAccess;
using CPHI.Spark.Model;
using CPHI.Spark.Model.FltOrd;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.ViewModels.RRG;
using CPHI.Spark.Repository;
using CPHI.Spark.Model;
using Dapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using OfficeOpenXml.FormulaParsing.Excel.Functions.RefAndLookup;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CPHI.Spark.WebApp.DataAccess
{
   public interface IFleetOrderbookDataAccess
   {
      Task<int> AddNewComment(FleetOrderbookNewCommentParams comment, Model.DealerGroupName dealerGroup);
      Task<IEnumerable<FleetOrderComment>> AddNewComments(FleetOrderbookNewCommentsParams comment, Model.DealerGroupName dealerGroup);
      Task DeleteComment(int commentId, int userId, Model.DealerGroupName dealerGroup);
      Task DeleteTableState(string state, int userId, Model.DealerGroupName dealerGroup);
      Task<IEnumerable<FleetOrderbookEditableOption>> GetEditableOptions(Model.DealerGroupName dealerGroup);
      //Task<IEnumerable<FleetOrderbookSummaryRow>> GetFleetOrderbookSummaryRowsNissan(DealerGroupName dealerGroup);
      //Task<IEnumerable<FleetOrderbookSummaryRow>> GetFleetOrderbookSummaryRowsRenault(DealerGroupName dealerGroup);
      Task<FleetOrderbookTableState> GetLastLoadedTableState(bool isRenault, int userId, Model.DealerGroupName dealerGroup);
      Task<FleetOrderbookTableStateParams> GetTableState(string label, bool isRenault, int userId, Model.DealerGroupName dealerGroup);
      Task<IEnumerable<string>> GetTableStateLabels(bool isRenault, int userId, Model.DealerGroupName dealerGroup);
      Task<FleetOrderTableState> SaveTableState(FleetOrderbookTableStateParams parms, int userId, Model.DealerGroupName dealerGroup);
      Task UpdateComment(FleetOrderbookUpdatedCommentParams comment, Model.DealerGroupName dealerGroup);
      Task<IEnumerable<FonName>> GetFonNames(Model.DealerGroupName dealerGroup);
      Task<FonName> UpdateFonName(FonName fonName, Model.DealerGroupName dealerGroup);
      Task<FonName> AddFonName(FonName fonName, Model.DealerGroupName dealerGroup);
      Task<FonName> DeleteFonName(FonName fonName, Model.DealerGroupName dealerGroup);
      Task UpdateProperties(FleetOrderbookUpdatePropsParams parms, Model.DealerGroupName dealerGroup);
      Task<IEnumerable<FleetOrderbookRowVM>> GetFleetOrderbookRowsRenault(Model.DealerGroupName dealerGroup, bool includeHidden, DateTime? includeRemovedAfter, List<int> rowIds = null);
      Task<IEnumerable<FleetOrderbookRowVM>> GetFleetOrderbookRowsNissan(Model.DealerGroupName dealerGroup, bool includeHidden, DateTime? includeRemovedAfter, List<int> rowIds = null);
      Task<IEnumerable<FleetOrderbookLastUpdatedDropdown>> GetLastUpdatedDates(Model.DealerGroupName dealerGroup);
   }

   public class FleetOrderbookDataAccess : IFleetOrderbookDataAccess
   {
      private readonly IDapperr dapper;
      private readonly CPHIDbContext db;
      private readonly IConfiguration config;

      public FleetOrderbookDataAccess(IDapperr dapper, CPHIDbContext db, IConfiguration config)
      {
         this.dapper = dapper;
         this.db = db;
         this.config = config;

      }

      //UnifiedDB - Updated
      public async Task<IEnumerable<FleetOrderbookRowVM>> GetFleetOrderbookRowsRenault(Model.DealerGroupName dealerGroup, bool includeHidden, DateTime? includeRemovedAfter, List<int> rowIds = null)
      {
         var parms = new DynamicParameters();//new { includeHidden, includeRemovedAfter });

         if (rowIds != null)
         {
            string rowIdsString = string.Join(',', rowIds);
            parms.Add("rowIds", rowIdsString);
         }
         else
         {
            parms.Add("rowIds", null);
         }
         parms.Add("includeHidden", includeHidden);
         parms.Add("includeRemovedAfter", includeRemovedAfter);
         parms.Add("dealerGroupId", (int)dealerGroup);


         var res = await dapper.GetAllAsync<FleetOrderbookRowVM>("dbo.GET_FleetOrderbookRowsRenault", parms, dealerGroup, System.Data.CommandType.StoredProcedure, 300);
         return res;
      }

      //UnifiedDB - Updated
      public async Task<IEnumerable<FleetOrderbookRowVM>> GetFleetOrderbookRowsNissan(Model.DealerGroupName dealerGroup, bool includeHidden, DateTime? includeRemovedAfter, List<int> rowIds = null)
      {
         var parms = new DynamicParameters(new { includeHidden, includeRemovedAfter });
         if (rowIds != null)
         {
            string rowIdsString = string.Join(',', rowIds);
            parms.Add("rowIds", rowIdsString);
         }
         parms.Add("dealerGroupId", (int)dealerGroup);

         var res = await dapper.GetAllAsync<FleetOrderbookRowVM>("dbo.GET_FleetOrderbookRowsNissan", parms, dealerGroup);
         return res;
      }

      //UnifiedDB - Updated, TODO: check SP
      public async Task<IEnumerable<FleetOrderbookEditableOption>> GetEditableOptions(Model.DealerGroupName dealerGroup)
      {
         var parms = new DynamicParameters(new { dealerGroupId = (int)dealerGroup });
         var res = await dapper.GetAllAsync<FleetOrderbookEditableOption>("dbo.GET_FleetOrderbookEditableOptions", parms, dealerGroup);
         return res;
      }


      //UnifiedDB - Updated
      public async Task<IEnumerable<FleetOrderbookLastUpdatedDropdown>> GetLastUpdatedDates(Model.DealerGroupName dealerGroup)
      {
         var parms = new DynamicParameters(new { dealerGroupId = (int)dealerGroup });
         return await dapper.GetAllAsync<FleetOrderbookLastUpdatedDropdown>("[fltord].[GET_LastUpdateDates]", parms, dealerGroup);
      }

      //UnifiedDB - no update required
      public async Task<FleetOrderTableState> SaveTableState(FleetOrderbookTableStateParams parms, int userId, Model.DealerGroupName dealerGroup)
      {
         Startup.SetDBContextConnectionString(dealerGroup, db);
         var existingState = db.FleetOrderTableStates.FirstOrDefault(x => x.Label == parms.Label && x.IsRenault == parms.IsRenault && x.Person_Id == userId);

         FleetOrderTableState toReturn = null;
         if (existingState != null)
         {
            //already exists, need to update
            existingState.State = parms.State;
            existingState.IsPivoted = parms.IsPivoted;
            existingState.FilterModel = parms.FilterModel;
            await db.SaveChangesAsync();
            toReturn = existingState;
         }
         else
         {


            //is new
            FleetOrderTableState toPersist = new FleetOrderTableState()
            {
               Label = parms.Label,
               IsRenault = parms.IsRenault,
               State = parms.State,
               FilterModel = parms.FilterModel,
               IsPivoted = parms.IsPivoted,
               Person_Id = userId,
               IsLastLoaded = true
            };
            var res = db.FleetOrderTableStates.AddAsync(toPersist);
            await db.SaveChangesAsync();
            toReturn = toPersist;
         }


         await GetTableState(parms.Label, parms.IsRenault, userId, dealerGroup); //in order to set the state we just saved to being the latest.  
         return toReturn;


      }


      //UnifiedDB - no update required    
      public async Task<int> AddNewComment(FleetOrderbookNewCommentParams comment, Model.DealerGroupName dealerGroup)
      {
         Startup.SetDBContextConnectionString(dealerGroup, db);
         FleetOrderComment commentToPersist = new FleetOrderComment()
         {
            Date = DateTime.UtcNow,
            Text = comment.Text,
            PersonId = (int)comment.UserId,
         };
         if (comment.IsRenault) { commentToPersist.RenaultOrderItem_Id = comment.FleetOrderbookId; }
         else { commentToPersist.NissanOrderItem_Id = comment.FleetOrderbookId; }

         await db.FleetOrderComments.AddAsync(commentToPersist);
         await db.SaveChangesAsync();
         return commentToPersist.Id;
      }

      //UnifiedDB - no update required    
      public async Task<IEnumerable<FleetOrderComment>> AddNewComments(FleetOrderbookNewCommentsParams comment, Model.DealerGroupName dealerGroup)
      {
         Startup.SetDBContextConnectionString(dealerGroup, db);
         List<FleetOrderComment> commentsToPersist = new List<FleetOrderComment>();

         foreach (var rowId in comment.FleetOrderbookIds)
         {
            int? renaultOrderItemId = null;
            int? nissanOrderItemId = null;
            if (comment.IsRenault) { renaultOrderItemId = rowId; }
            else { nissanOrderItemId = rowId; }


            commentsToPersist.Add(new FleetOrderComment()
            {
               RenaultOrderItem_Id = renaultOrderItemId,
               NissanOrderItem_Id = nissanOrderItemId,
               Date = DateTime.UtcNow,
               Text = comment.Text,
               PersonId = (int)comment.UserId,
            });
         }


         await db.FleetOrderComments.AddRangeAsync(commentsToPersist);
         await db.SaveChangesAsync();
         return commentsToPersist;
      }



      //UnifiedDB - TODO: StockCategoryItems & AlarmDataItems & OrderTrackingItems do not have DealerGroupId
      public async Task UpdateProperties(FleetOrderbookUpdatePropsParams parms, Model.DealerGroupName dealerGroup)
      {
         Startup.SetDBContextConnectionString(dealerGroup, db);

         // ------------------------------------------------------------
         // Step 1 - Based on the RenaultOrderItemIds or
         // NissanOrderItemIds, get the associated OrderTrackingItemIds
         // ------------------------------------------------------------
         string query = parms.IsRenault ? GenerateRenaultQuery(parms, dealerGroup) : GenerateNissanQuery(parms, dealerGroup);
         Dictionary<int, FleetOrderbookOrderItemAndTrackingItemId> itemLookup = await GetOrderTrackingItemIdLookup(parms, query, dealerGroup);


         // ------------------------------------------------------------
         // Step 2 - Do the create or update
         // ------------------------------------------------------------
         FleetOrderbookTrackingItemUpdateKeyObjects keyObjects = new FleetOrderbookTrackingItemUpdateKeyObjects()
         {
            //fonNames = await db.FonNames.ToListAsync(),
            stockCategoryItems = await db.StockCategoryItems.ToListAsync(),
            alarmDataItems = await db.AlarmDataItems.ToListAsync(),
         };

         List<OrderTrackingItem> newItems = new List<OrderTrackingItem>();
         foreach (var row in parms.UpdatedRows)
         {
            try
            {
               var lookupMatch = itemLookup[row.RowId];
               if (lookupMatch == null)
               {
                  //must require a new item
                  var newItem = new OrderTrackingItem();
                  if (parms.IsRenault) { newItem.RenaultOrderItemId = row.RowId; }
                  else { newItem.NissanOrderItemId = row.RowId; }
                  UpdateOrderTrackingItemProps(row, newItem, keyObjects);
                  newItems.Add(newItem);
               }
               else
               {
                  //we have an orderTrackingItem
                  int? orderTrackingItemId = lookupMatch.OrderTrackingItemId;
                  if (orderTrackingItemId == null)
                  {
                     { }
                  }
                  var orderTrackingItem = await db.OrderTrackingItems.FirstAsync(x => x.Id == orderTrackingItemId);
                  UpdateOrderTrackingItemProps(row, orderTrackingItem, keyObjects);
               }
            }
            catch (Exception ex)
            {
               { }
            }
         }
         if (newItems.Count > 0)
         {
            db.OrderTrackingItems.AddRange(newItems);
         }

         await db.SaveChangesAsync();



      }



      //UnifiedDB - no updates required
      private async Task<Dictionary<int, FleetOrderbookOrderItemAndTrackingItemId>> GetOrderTrackingItemIdLookup(FleetOrderbookUpdatePropsParams parms, string query, Model.DealerGroupName dealerGroup)
      {
         IEnumerable<FleetOrderbookOrderItemAndTrackingItemId> existingItems = await dapper.GetAllAsync<FleetOrderbookOrderItemAndTrackingItemId>(query, null, dealerGroup, System.Data.CommandType.Text);
         Dictionary<int, FleetOrderbookOrderItemAndTrackingItemId> itemLookup = existingItems.ToDictionary(x => x.OrderItemId);
         return itemLookup;
      }

      //UnifiedDB - updated
      private static string GenerateRenaultQuery(FleetOrderbookUpdatePropsParams parms, DealerGroupName dealerGroup)
      {
         return $@"SELECT
ro.Id as OrderItemId,
oti.Id as OrderTrackingItemId
FROM fltord.RenaultOrderItems ro
LEFT JOIN fltord.OrderTrackingItems oti on oti.RenaultOrderItemId = ro.Id
WHERE ro.DealerGroup_Id = {(int)dealerGroup} AND ro.Id IN ({string.Join(',', parms.UpdatedRows.Select(x => x.RowId).ToList())})";
      }

      //UnifiedDB - updated
      private static string GenerateNissanQuery(FleetOrderbookUpdatePropsParams parms, DealerGroupName dealerGroup)
      {
         return $@"SELECT
no.Id as OrderItemId,
oti.Id as OrderTrackingItemId
FROM fltord.NissanOrderItems no
LEFT JOIN fltord.OrderTrackingItems oti on oti.NissanOrderItemId = no.Id
WHERE no.DealerGroup_Id = {(int)dealerGroup} AND no.Id IN ({string.Join(',', parms.UpdatedRows.Select(x => x.RowId).ToList())})";
      }


      //UnifiedDB - no upate required
      // This function will be accessed when the user manually updates rows via the FleetOrderbook page
      private static void UpdateOrderTrackingItemProps(FleetOrderbookUpdatedRow row, OrderTrackingItem orderTrackingItem, FleetOrderbookTrackingItemUpdateKeyObjects keyObjects)
      {
         orderTrackingItem.SalesPerson = row.SalesPerson;
         orderTrackingItem.AccountHandlerName = row.AccountHandlerName;

         // New columns - 1st Sept 2025
         orderTrackingItem.DeliveryAgent = row.DeliveryAgent;
         orderTrackingItem.PivgReference = row.PivgReference;

         orderTrackingItem.EndUser = row.EndUser;
         orderTrackingItem.CustomerOrderNo = row.CustomerOrderNo;
         orderTrackingItem.DeliveryDepot = row.DeliveryDepot;
         orderTrackingItem.FonCode = row.FonCode;

         // If the tracking item does not have a Reg, do not add it.
         // This will allow it to continue to pull through CDK/Report Reg
         if (!string.IsNullOrEmpty(orderTrackingItem.Reg))
         {
            orderTrackingItem.Reg = row.Reg;
         }

         if (!string.IsNullOrEmpty(row.AlarmData))
         {
            var matchingAlarmDataItem = keyObjects.alarmDataItems.First(x => x.AlarmData == row.AlarmData);
            orderTrackingItem.AlarmDataItemId = matchingAlarmDataItem.Id;

         }
         else
         {
            orderTrackingItem.AlarmDataItemId = null;
         }
         if (!string.IsNullOrEmpty(row.StockCategory))
         {
            var matchingStockCategory = keyObjects.stockCategoryItems.First(x => x.StockCategory == row.StockCategory);
            orderTrackingItem.StockCategoryItemId = matchingStockCategory.Id;
         }
         else
         {
            orderTrackingItem.StockCategoryItemId = null;
         }

         // Date props 
         // This can be cleared and made nullable
         orderTrackingItem.DeliveryDate = row.DeliveryDate;
         orderTrackingItem.InvoiceDate = row.InvoiceDate;
         orderTrackingItem.ConvBldDte = row.ConvBldDte;
         orderTrackingItem.ConvEstComplDte = row.ConvEstComplDte;
         //orderTrackingItem.ConverterBuildStartDate = row.ConverterBuildStartDate;

         //bool props
         orderTrackingItem.DriverPackOrdered = (bool)row.DriverPackOrdered;
         orderTrackingItem.DriverPackRequired = (bool)row.DriverPackRequired;
         orderTrackingItem.IsHidden = (bool)row.IsHidden;

         //other
         if (orderTrackingItem.RemovedDate == null && row.IsRemoved)
         {
            //we wish to set the item to removed
            orderTrackingItem.RemovedDate = DateTime.Now.Date;
         }
         else if (!row.IsRemoved)
         {
            //we wish to set the item to not removed.
            orderTrackingItem.RemovedDate = null;
         }

         //SPK-4313 4 new fields
         if (row.RUKIsGoing == "Not Going")
         {
            orderTrackingItem.RUKIsGoing = "Not Going";
            orderTrackingItem.RUKForecastReason = row.RUKForecastReason;
            orderTrackingItem.RUKForecastSubReason = row.RUKForecastSubReason;
            orderTrackingItem.RUKForecastFRD = row.RUKForecastFRD;
         }
         else if (row.RUKIsGoing == "Going")
         {
            orderTrackingItem.RUKIsGoing = "Going";
            orderTrackingItem.RUKForecastReason = null;
            orderTrackingItem.RUKForecastSubReason = null;
            orderTrackingItem.RUKForecastFRD = null;
         }
         else
         {
            orderTrackingItem.RUKIsGoing = null;
            orderTrackingItem.RUKForecastReason = null;
            orderTrackingItem.RUKForecastSubReason = null;
            orderTrackingItem.RUKForecastFRD = null;
         }

         orderTrackingItem.ConverterName = row.ConverterName;



      }


      //UnifiedDB - no update required
      public async Task UpdateComment(FleetOrderbookUpdatedCommentParams comment, Model.DealerGroupName dealerGroup)
      {
         Startup.SetDBContextConnectionString(dealerGroup, db);
         var existingItem = db.FleetOrderComments.First(x => x.Id == comment.CommentId);
         if (existingItem.PersonId != comment.UserId)
         {
            throw new Exception("Unable to update other users comment");
         }
         existingItem.Text = comment.Text;
         await db.SaveChangesAsync();
         return;
      }

      //UnifiedDB - no update required
      public async Task DeleteComment(int commentId, int userId, Model.DealerGroupName dealerGroup)
      {
         Startup.SetDBContextConnectionString(dealerGroup, db);
         var item = db.FleetOrderComments.FirstOrDefault(x => x.Id == commentId);
         if (item == null)
         {
            throw new Exception("Could not find comment");
         }
         if (item.PersonId != userId)
         {
            throw new Exception("Unable to update other users comment");
         }

         db.FleetOrderComments.Remove(item);
         await db.SaveChangesAsync();
         return;
      }

      //UnifiedDB - TODO: check if UserId check like above is required here as well
      public async Task DeleteTableState(string label, int userId, Model.DealerGroupName dealerGroup)
      {
         await dapper.ExecuteAsync($"DELETE FROM fltord.FleetOrderTableStates WHERE Label = '{label}' AND Person_Id = {userId}", null, dealerGroup, System.Data.CommandType.Text);
      }


      //UnifiedDB - no update required
      public async Task<FleetOrderbookTableStateParams> GetTableState(string label, bool isRenault, int userId, Model.DealerGroupName dealerGroup)
      {
         Startup.SetDBContextConnectionString(dealerGroup, db);
         var userStates = await db.FleetOrderTableStates.Where(x => x.IsRenault == isRenault && x.Person_Id == userId).ToListAsync();
         var state = userStates.FirstOrDefault(x => x.Label == label);

         if (state == null)
         {
            throw new Exception("Not found");
         }

         foreach (var item in userStates)
         {
            item.IsLastLoaded = (item.Label == label);
         }

         await db.SaveChangesAsync();

         return new FleetOrderbookTableStateParams()
         {
            State = state.State,
            FilterModel = state.FilterModel,
            IsPivoted = state.IsPivoted,
            Label = state.Label,
            IsRenault = state.IsRenault
         };

      }

      //UnifiedDB - no update required
      public async Task<IEnumerable<string>> GetTableStateLabels(bool isRenault, int userId, Model.DealerGroupName dealerGroup)
      {
         Startup.SetDBContextConnectionString(dealerGroup, db);
         var items = await db.FleetOrderTableStates.Where(x => x.IsRenault == isRenault && x.Person_Id == userId).ToListAsync();
         return items.Select(x => x.Label);
      }

      //UnifiedDB - no update required
      public async Task<FleetOrderbookTableState> GetLastLoadedTableState(bool isRenault, int userId, Model.DealerGroupName dealerGroup)
      {
         //Startup.SetDBContextConnectionString(dealerGroup, db);
         Startup.SetDBContextConnectionString(dealerGroup, db);
         var state = await db.FleetOrderTableStates.FirstOrDefaultAsync(x => !x.IsPivoted && x.IsRenault == isRenault && x.Person_Id == userId && x.IsLastLoaded);

         if (state != null)
         {
            return new FleetOrderbookTableState() { State = state.State, IsPivoted = state.IsPivoted, Label = state.Label, FilterModel = state.FilterModel };
         }
         else
         {
            return null;
         }
      }

      //UnifiedDB - updated
      public async Task<IEnumerable<FonName>> GetFonNames(Model.DealerGroupName dealerGroup)
      {
         Startup.SetDBContextConnectionString(dealerGroup, db);
         return await db.FonNames.Where(x => x.DealerGroup_Id == (int)dealerGroup).ToListAsync();
      }

      //UnifiedDB - updated
      public async Task<FonName> UpdateFonName(FonName fonName, Model.DealerGroupName dealerGroup)
      {
         Startup.SetDBContextConnectionString(dealerGroup, db);
         var existingFonName = db.FonNames.First(x => x.Id == fonName.Id && x.DealerGroup_Id == (int)dealerGroup);

         existingFonName.Code = fonName.Code;
         existingFonName.Description = fonName.Description;

         await db.SaveChangesAsync();
         return existingFonName;
      }

      //UnifiedDB - updated
      public async Task<FonName> AddFonName(FonName fonName, Model.DealerGroupName dealerGroup)
      {
         if (fonName.DealerGroup_Id != (int)dealerGroup) throw new Exception("Invalid dealer group");

         Startup.SetDBContextConnectionString(dealerGroup, db);
         await db.FonNames.AddAsync(fonName);
         await db.SaveChangesAsync();
         return fonName;
      }

      //UnifiedDB - updated
      public async Task<FonName> DeleteFonName(FonName fonName, Model.DealerGroupName dealerGroup)
      {
         if (fonName.DealerGroup_Id != (int)dealerGroup) throw new Exception("Invalid dealer group");

         Startup.SetDBContextConnectionString(dealerGroup, db);
         db.FonNames.Remove(fonName);
         await db.SaveChangesAsync();
         return fonName;
      }




   }
}