import {Component, HostListener, OnDestroy, OnInit} from '@angular/core';
import {ActivatedRoute, NavigationEnd, Router} from '@angular/router';
import {filter} from 'rxjs/operators';
import {Subscription} from 'rxjs';
import {MenuItemNew, MenuSection} from 'src/app/model/main.model';
import {PreferenceKey} from 'src/app/model/UserPreference';
import {DashboardService} from 'src/app/pages/dashboard/dashboard.service';
import {ConstantsService} from 'src/app/services/constants.service';
import {SelectionsService} from 'src/app/services/selections.service';
import {UserPreferenceService} from 'src/app/services/userPreference.service';

@Component({
   selector: 'sidenav',
   templateUrl: './sidenav.component.html',
   styleUrls: ['./sidenav.component.scss']
})
export class SidenavComponent implements OnInit, OnDestroy {
   menuIsWide: boolean;
   showMenuLabels: boolean;
   showSubMenuLabels: boolean;
   keepMenuWide: boolean;
   amHoveringSideMenu: boolean;

   private routerSubscription: Subscription;

   get keepMenuFixed(): boolean {
      return this.userPrefsService.getPreference(PreferenceKey.KeepMenuFixed);
   }

   @HostListener('document:click', ['$event'])
   detectClickOutsideMenu(event) {
      if (event.pageX > 200) {
         this.keepMenuWide = false;
         this.closeSideMenu();
      }
   }

   constructor(
      public selections: SelectionsService,
      public currentRoute: ActivatedRoute,
      private userPrefsService: UserPreferenceService,
      public router: Router,
      public constants: ConstantsService,
      public dashboardService: DashboardService
   ) {
   }

   ngOnInit(): void {
      if (this.userPrefsService.getPreference(PreferenceKey.KeepMenuFixed)) {
         this.makeMenuWide();
      }

      // Set initial active menu based on current URL
      this.constants.highlightActiveMenuBasedOnUrl();

      // Subscribe to route changes to update active menu indicator
      this.routerSubscription = this.router.events
         .pipe(filter(event => event instanceof NavigationEnd))
         .subscribe(() => {
            this.constants.highlightActiveMenuBasedOnUrl();
         });
   }

   ngOnDestroy(): void {
      if (this.routerSubscription) {
         this.routerSubscription.unsubscribe();
      }
   }

   recordOpenMenuItems() {
      const openItems = this.constants.menuSections.filter(x => x.expanded).map(x => x.name);
      this.userPrefsService.setPreference(PreferenceKey.OpenSideMenuItems, openItems);
   }

   makeMenuWide() {
      this.menuIsWide = true;

      setTimeout(() => {
         this.showMenuLabels = true;
         this.showSubMenuLabels = true;
      }, 50);
   }

   onMenuSectionClicked($event: any, menuSection: MenuSection, group?: string) {
      //this.constants.chosenPage = menuSection;
      // if (menuSection.link === '/dashboard') {
      //   this.dealWithDashboardLink(menuSection, group);
      // }
      // else {
      //if (menuItem.subItems) {
      menuSection.expanded = !menuSection.expanded;
      this.recordOpenMenuItems();
      //}
      // else {
      //   this.constants.navigateByUrl(menuItem, group);
      // }
      //}
   }

   onMenuItemClicked($event: any, menuItem: MenuItemNew) {
      this.constants.chosenPage = menuItem;
      if (menuItem.link.includes('dashboard')) {
         this.dealWithDashboardLink(menuItem);
      }
      this.constants.navigateByUrl(menuItem);
   }

   private dealWithDashboardLink(menuItem: MenuItemNew) {
      this.dashboardService.chosenPage = {
         pageName: menuItem.pageName,
         translatedTextField: menuItem.pageName,
         translatedTextValue: menuItem.pageName
      };

      if (menuItem.isSales) {
         this.dashboardService.chosenSection = this.constants.environment.dashboard_sections.find(x => x.translatedTextField === 'Common_Sales');
      } else {
         this.dashboardService.chosenSection = this.constants.environment.dashboard_sections.find(x => x.translatedTextField === 'Common_Aftersales');
      }
   }

   closeSideMenu() {
      if (this.keepMenuFixed) {
         return;
      }
      this.showSubMenuLabels = false;
      this.showMenuLabels = false;
      this.menuIsWide = false;
   }

   routeIsActive(menuItem: MenuItemNew) {
      return menuItem.link === this.currentRoute.snapshot['_routerState'].url;
   }

   togglePersistMenu() {
      this.keepMenuWide = !this.keepMenuWide;
   }


   maybeHideMenu(event) {
      this.amHoveringSideMenu = false;

      const sidenavWidth: number = document.getElementById('sidenav').clientWidth;
      if (this.keepMenuWide || this.userPrefsService.getPreference(PreferenceKey.KeepMenuFixed) || event.pageX < sidenavWidth) {
         return;
      }
      this.closeSideMenu();
   }

   fixMenu() {
      const isFixed: boolean = this.userPrefsService.getPreference(PreferenceKey.KeepMenuFixed);

      if (isFixed) {
         this.closeSideMenu();
         this.userPrefsService.setPreference(PreferenceKey.KeepMenuFixed, false);
      } else {
         this.userPrefsService.setPreference(PreferenceKey.KeepMenuFixed, true);
      }

   }

   showFixMenuToggle() {
      const keepMenuFixed = this.userPrefsService.getPreference(PreferenceKey.KeepMenuFixed);
      return keepMenuFixed ? this.amHoveringSideMenu : this.showMenuLabels;
   }
}
