import { FleetOrderbookRow } from "./FleetOrderbookRow";


export class FleetOrderbookUpdatedRow {

  constructor(row: FleetOrderbookRow) {
    this.RowId = row.RenaultOrNissanOrderItemId;
    this.SalesPerson = row.SalesPerson;
    this.EndUser = row.EndUser;
    this.CustomerOrderNo = row.CustomerOrderNo;
    this.DeliveryDepot = row.DeliveryDepot;
    this.InvoiceDate = row.InvoiceDateToCustomer;
    this.FonDescription = row.FonDescription;
    this.FonCode = row.FonCode;
    this.Reg = row.Reg;
    this.AlarmData = row.AlarmData;
    this.StockCategory = row.StockCategory;
    this.DriverPackRequired = row.DriverPackRequired;
    this.DriverPackOrdered = row.DriverPackOrdered;
    this.DeliveryDate = row.DeliveryDate ? new Date(row.DeliveryDate) : null;
    this.IsHidden = row.IsHidden;
    this.IsRemoved = row.IsRemoved;
    this.ConverterName = row.ConverterName;
    this.AccountHandlerName = row.AccountHandlerName;
    this.ConvBldDte = row.ConvBldDte;
    this.ConvEstComplDte = row.ConvEstComplDte;

    this.RUKIsGoing = row.RUKIsGoing;
    this.RUKForecastReason = row.RUKForecastReason;
    this.RUKForecastSubReason = row.RUKForecastSubReason;

    this.RUKForecastFRD = null;

    if (row.RUKForecastFRD != null && this.isValidDate(row.RUKForecastFRD)) {
        const forecastDate = new Date(row.RUKForecastFRD);
        if (!isNaN(forecastDate.getTime())) {
            this.RUKForecastFRD = forecastDate.toISOString();
        }
    }

    this.PivgReference = row.PivgReference;
    this.DeliveryAgent = row.DeliveryAgent;

  }

  isValidDate(date: any): boolean {
      if (date instanceof Date && !isNaN(date.getTime())) {
          return true;
      }
      if (typeof date === 'string') {
          const parsedDate = new Date(date);
          return !isNaN(parsedDate.getTime());
      }
      return false;
  }
  
  RowId: number;
  //the updated properties
  SalesPerson: string;
  EndUser: string;
  CustomerOrderNo: string;
  DeliveryDepot: string;
  InvoiceDate: Date | string | null;
  FonDescription: string;
  FonCode: string;
  AlarmData: string;
  StockCategory: string;
  DriverPackRequired: boolean;
  DriverPackOrdered: boolean;
  DeliveryDate: Date | string | null;
  IsHidden: boolean;
  IsRemoved: boolean;
  Reg: string;
  ConverterName:string;
  AccountHandlerName:string;
  ConvBldDte:Date|string|null;
  ConvEstComplDte:Date|string|null;


  RUKIsGoing: string;
  RUKForecastReason: string;
  RUKForecastSubReason: string;
  RUKForecastFRD: Date | string | null;
  PivgReference: string;
  DeliveryAgent: string;
}
