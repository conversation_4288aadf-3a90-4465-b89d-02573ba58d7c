using log4net;
using OpenQA.Selenium;
using OpenQA.Selenium.Chrome;
using OpenQA.Selenium.Support.UI;
using Quartz;
using SeleniumExtras.WaitHelpers;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading.Tasks;
using System.Threading;
using CPHI.Spark.WebScraper.Services;

namespace CPHI.Spark.WebScraper.Jobs
{
    [DisallowConcurrentExecution]
    public class DownloadDragonReportJob : IJob
    {
        private static readonly ILog logger = LogManager.GetLogger(typeof(DownloadDragonReportJob));
        private static IWebDriver _driver;
        private static CommonMethods _commonMethods;

        public void Execute() { }

        public async Task Execute(IJobExecutionContext context)
        {
            Stopwatch stopwatch = new Stopwatch();
            string errorMessage = string.Empty;
            stopwatch.Start();

            try
            {
                logger.Info("");
                logger.Info($"========================= Starting {GetType().Name} =====================================");

                // Initialize Chrome driver
                var service = ChromeDriverService.CreateDefaultService();
                service.HostName = "127.0.0.1";

                ChromeOptions options = ScraperMethodsService.SetChromeOptions("DragonReport", 9229);
                _driver = new ChromeDriver(service, options, TimeSpan.FromSeconds(130));
                _commonMethods = new CommonMethods(_driver, logger);

                // Perform login
                bool loginSuccessful = await LoginAsync();

                if (loginSuccessful)
                {
                    logger.Info("Login successful. Starting report download process...");
                    
                    // TODO: Add report download logic here
                    await DownloadReportAsync();
                    
                    logger.Info("Report download completed successfully.");
                }
                else
                {
                    logger.Error("Login failed. Cannot proceed with report download.");
                    errorMessage = "Login failed";
                }
            }
            catch (Exception e)
            {
                logger.Error($"Error in {GetType().Name}: {e.Message}", e);
                errorMessage = e.Message;
            }
            finally
            {
                try
                {
                    _driver?.Quit();
                    _driver?.Dispose();
                }
                catch (Exception ex)
                {
                    logger.Warn($"Error disposing driver: {ex.Message}");
                }

                stopwatch.Stop();
                logger.Info($"========================= Finished {GetType().Name} in {stopwatch.Elapsed} =====================================");
                
                if (!string.IsNullOrEmpty(errorMessage))
                {
                    throw new Exception(errorMessage);
                }
            }
        }

        private async Task<bool> LoginAsync()
        {
            const int maxAttempts = 5;
            const int delayBetweenAttemptsMs = 10000;

            for (int attempt = 1; attempt <= maxAttempts; attempt++)
            {
                logger.Info($"Login attempt {attempt} of {maxAttempts}...");

                try
                {
                    // Navigate to login page - TODO: Replace with actual Dragon Report URL
                    _driver.Navigate().GoToUrl("https://dragonreport.example.com/login");

                    WebDriverWait wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(10));

                    // TODO: Update these selectors based on actual Dragon Report login form
                    // Enter PIN
                    IWebElement pinField = wait.Until(ExpectedConditions.ElementIsVisible(By.Id("pin")));
                    pinField.Clear();
                    pinField.SendKeys(ConfigService.DragonReportPin);

                    // Enter Username
                    IWebElement usernameField = wait.Until(ExpectedConditions.ElementIsVisible(By.Id("username")));
                    usernameField.Clear();
                    usernameField.SendKeys(ConfigService.DragonReportUsername);

                    // Enter Password
                    IWebElement passwordField = wait.Until(ExpectedConditions.ElementIsVisible(By.Id("password")));
                    passwordField.Clear();
                    passwordField.SendKeys(ConfigService.DragonReportPassword);

                    // Click login button
                    IWebElement loginButton = wait.Until(ExpectedConditions.ElementToBeClickable(By.Id("loginButton")));
                    loginButton.Click();

                    // Wait for login to complete and verify success
                    Thread.Sleep(3000);

                    // TODO: Update this selector based on actual Dragon Report dashboard element
                    try
                    {
                        IWebElement dashboardElement = wait.Until(ExpectedConditions.ElementExists(By.Id("dashboard")));
                        logger.Info("Login successful - dashboard element found");
                        return true;
                    }
                    catch
                    {
                        logger.Warn("Login verification failed - dashboard element not found");
                    }
                }
                catch (Exception ex)
                {
                    logger.Warn($"Login attempt {attempt} failed: {ex.Message}");
                }

                if (attempt < maxAttempts)
                {
                    logger.Info($"Waiting {delayBetweenAttemptsMs / 1000} seconds before next attempt...");
                    Thread.Sleep(delayBetweenAttemptsMs);
                }
            }

            logger.Error($"All {maxAttempts} login attempts failed");
            return false;
        }

        private async Task DownloadReportAsync()
        {
            try
            {
                WebDriverWait wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(10));

                // TODO: Implement the actual report download logic
                // This is a placeholder implementation
                logger.Info("Navigating to reports section...");
                
                // Example: Navigate to reports page
                // IWebElement reportsLink = wait.Until(ExpectedConditions.ElementToBeClickable(By.Id("reportsLink")));
                // reportsLink.Click();

                // Example: Select and download report
                // IWebElement downloadButton = wait.Until(ExpectedConditions.ElementToBeClickable(By.Id("downloadButton")));
                // downloadButton.Click();

                logger.Info("Report download logic placeholder - implement based on actual Dragon Report interface");
                
                // Wait for download to complete
                Thread.Sleep(5000);
            }
            catch (Exception ex)
            {
                logger.Error($"Error during report download: {ex.Message}", ex);
                throw;
            }
        }

        private IWebElement WaitAndFind(string xpath, bool andClick = false)
        {
            return ScraperMethodsService.WaitAndFind(_driver, "DownloadDragonReport", xpath, andClick);
        }
    }
}
