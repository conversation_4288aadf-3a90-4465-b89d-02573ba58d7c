﻿using System;
using System.Collections.Generic;

namespace CPHI.Spark.Model.ViewModels.AutoPricing
{
    public class TodaySiteStats
    {
        public TodaySiteStats() { }
        public TodaySiteStats(List<TodaySiteStats> statsIn)
        {
            Name = "Total";
            RetailerSiteId = 0;
            //PriceRuleSetCreatedBy = null;
            //PriceRuleSetEffectiveDate = null;
            ListedCount = 0;
            AutoPriceChanges = 0;
            ApprovedCount = 0;
            NoRating = 0;
            RetailRating_u20 = 0;
            RetailRating_u40 = 0;
            RetailRating_u60 = 0;
            RetailRating_u80 = 0;
            RetailRating_o80 = 0;
            OptOutCount = 0;

            foreach (TodaySiteStats stat in statsIn)
            {
                ListedCount += stat.ListedCount;
                AutoPriceChanges += stat.AutoPriceChanges;
                AutoPriceChangesOptedOutOnDay += stat.AutoPriceChangesOptedOutOnDay;
                ApprovedCount += stat.ApprovedCount;
                NoRating += stat.NoRating;
                RetailRating_u20 += stat.RetailRating_u20;
                RetailRating_u40 += stat.RetailRating_u40;
                RetailRating_u60 += stat.RetailRating_u60;
                RetailRating_u80 += stat.RetailRating_u80;
                RetailRating_o80 += stat.RetailRating_o80;
                OptOutCount += stat.OptOutCount;
            }
        }
        public string Name { get; set; }
        public int RetailerSiteId { get; set; }
        public int ListedCount { get; set; }
        //public string PriceRuleSetCreatedBy { get; set; }
        //public DateTime? PriceRuleSetEffectiveDate { get; set; }
        public int AutoPriceChanges { get; set; }
        public int AutoPriceChangesOptedOutOnDay { get; set; }
        public int ApprovedCount { get; set; }
        public int NoRating { get; set; }
        public int RetailRating_u20 { get; set; }
        public int RetailRating_u40 { get; set; }
        public int RetailRating_u60 { get; set; }
        public int RetailRating_u80 { get; set; }
        public int RetailRating_o80 { get; set; }
        public int Total => RetailRating_u20 + RetailRating_u40 + RetailRating_u60 + RetailRating_u80 + RetailRating_o80;
        public int OptOutCount { get; set; }
        public int AutoPriceChangesScheduled => AutoPriceChanges - AutoPriceChangesOptedOutOnDay;
        public int SiteId { get; set; }
        
    }

}
