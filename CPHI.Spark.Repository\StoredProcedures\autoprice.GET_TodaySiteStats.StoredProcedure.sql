/****** Object:  StoredProcedure [autoprice].[GET_TodaySiteStats]    Script Date: 01/03/2024 10:00:44 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER    PROCEDURE [autoprice].[GET_TodaySiteStats]
(
		@chosenDate DATE,
		@userEligibleSites nvarchar(MAX) = null,
		@includeNewVehicles bit = 0,
		@includeUsedVehicles bit = 0,
		@dealerGroupId int
)
  
AS  
BEGIN  


	SELECT Value as Id INTO #eligibleSites from STRING_SPLIT(@userEligibleSites,',') 

	DECLARE @optOutSinceGeneratedStartPoint DateTime = DATETIMEFROMPARTS (YEAR(@chosenDate),MON<PERSON>(@chosenDate),DAY(@chosenDate),6,0,0,0);
	DECLARE @optOutSinceGeneratedEndPoint DateTime = DATETIMEFROMPARTS (YEAR(@chosenDate),<PERSON><PERSON><PERSON>(@chosenDate),<PERSON><PERSON><PERSON>(@chosenDate),18,0,0,0);
	DECLARE @chosenDatePlus1 Date = DATEADD(day,1,@chosenDate);	

SET NOCOUNT ON;  

----------------------------------------------
--Sub table for the most recent retail ratings
----------------------------------------------
SELECT snaps.VehicleAdvert_Id, MAX(snaps.Id) as Id, snaps.RetailRating
INTO #veryLatestRetailRatingsPerAd
FROM autoprice.VehicleAdvertSnapshots snaps
INNER JOIN autoprice.VehicleAdverts ads on ads.id  = snaps.VehicleAdvert_Id
INNER JOIN autoprice.RetailerSites rs on rs.Id = ads.RetailerSite_Id
WHERE snaps.SnapshotDate >= @chosenDate AND snaps.SnapshotDate < @chosenDatePlus1
AND rs.IsActive = 1
AND rs.DealerGroup_Id = @dealerGroupId
AND snaps.LifecycleStatus <> 'WASTEBIN'
AND snaps.LifecycleStatus <> 'SOLD' 
AND
(
	ads.AutotraderAdvertStatus = 'PUBLISHED' OR 
	rs.IncludeUnPublishedAds = 1
)

GROUP BY snaps.VehicleAdvert_Id, snaps.RetailRating



----------------------------------------------
--Sub table for total vehicles listed
----------------------------------------------
SELECT
ads.RetailerSite_Id as RetailerSiteId,
COUNT(ratings.Id) as VehiclesCount
INTO #vehiclesListed
FROM #veryLatestRetailRatingsPerAd ratings
INNER JOIN autoprice.VehicleAdverts ads on ads.Id = ratings.VehicleAdvert_Id
INNER JOIN autoprice.RetailerSites rs on rs.Id = ads.RetailerSite_Id
WHERE
(
	(ads.OwnershipCondition = 'New' AND @includeNewVehicles = 1)
	OR
	(ads.OwnershipCondition = 'Used' AND @includeUsedVehicles = 1)
	
)
AND rs.DealerGroup_Id = @dealerGroupId
AND rs.IsActive = 1
GROUP BY ads.RetailerSite_Id
	


----------------------------------------------
--Sub table for vehicles by Retail Rating
----------------------------------------------
SELECT 
* 
INTO #vehiclesByRetailRating
FROM
(
	SELECT
	ads.RetailerSite_Id as RetailerSiteId,
	CASE
	WHEN ratings.RetailRating IS NULL THEN 'not available'
		WHEN ratings.RetailRating <20 THEN 'u20'
		WHEN ratings.RetailRating <40 THEN 'u40'
		WHEN ratings.RetailRating <60 THEN 'u60'
		WHEN ratings.RetailRating <80 THEN 'u80'
		ELSE 'o80'
	END as RatingBanding,
	COUNT(ratings.Id) as VehiclesCount
	FROM #veryLatestRetailRatingsPerAd ratings
	INNER JOIN autoprice.VehicleAdverts ads on ads.Id = ratings.VehicleAdvert_Id
	WHERE
	(
		(ads.OwnershipCondition = 'New' AND @includeNewVehicles = 1)
		OR
		(ads.OwnershipCondition = 'Used' AND @includeUsedVehicles = 1)
	)
	GROUP BY ads.RetailerSite_Id,
	CASE
	WHEN ratings.RetailRating IS NULL THEN 'not available'
		WHEN ratings.RetailRating <20 THEN 'u20'
		WHEN ratings.RetailRating <40 THEN 'u40'
		WHEN ratings.RetailRating <60 THEN 'u60'
		WHEN ratings.RetailRating <80 THEN 'u80'
		ELSE 'o80'
	END 
) as sourcetable
PIVOT
(
	SUM(VehiclesCount)
	FOR RatingBanding IN ([not available],u20,u40,u60,u80,o80)
) as PivotTable




----------------------------------------------
--Sub table for auto price changes today
----------------------------------------------
SELECT
ads.RetailerSite_Id,
COUNT(pricechanges.Id) as AutoPriceChanges,
SUM(IIF(priceChanges.IsApproved=1,1,0)) as ApprovedCount,
SUM(IIF(opts.Id IS NOT NULL,1,0)) as OptedOutOnDay
INTO #autopricechangestoday
FROM autoprice.PriceChangeAutoItems pricechanges
INNER JOIN autoprice.VehicleAdvertSnapshots snaps on snaps.Id = pricechanges.VehicleAdvertSnapshot_Id
LEFT JOIN autoprice.VehicleAdvertSnapshots ratings on ratings.Id = pricechanges.VehicleAdvertSnapshot_Id
LEFT JOIN autoprice.VehicleAdverts ads on ads.id = ratings.VehicleAdvert_Id 
LEFT JOIN autoprice.RetailerSites rs on rs.Id = ads.RetailerSite_Id
LEFT JOIN autoprice.VehicleOptOuts opts on opts.VehicleAdvert_Id = ads.id AND opts.CreatedDate > @optOutSinceGeneratedStartPoint AND opts.ActualEndDate <= @optOutSinceGeneratedEndPoint
WHERE 
	(pricechanges.CouldGenerateNewPrice = 1 ) --OR pricechanges.WasOptedOutOfWhenGenerated = 1
AND CONVERT(date,pricechanges.CreatedDate) = @chosenDate
AND 
(
	(pricechanges.NowPrice - COALESCE(pricechanges.WasPrice, 0)) >= 100
	OR
	(pricechanges.NowPrice - COALESCE(pricechanges.WasPrice, 0)) <= -100
)
AND
(
	(ads.OwnershipCondition = 'New' AND @includeNewVehicles = 1)
	OR
	(ads.OwnershipCondition = 'Used' AND @includeUsedVehicles = 1)
)
AND rs.DealerGroup_Id = @dealerGroupId
--AND opts.Id IS NULL
GROUP BY ads.RetailerSite_Id;





----------------------------------------------
--Sub table for optouts.  
--------------------------------------------

select 
ads.RetailerSite_Id as RetailerSiteId,
COUNT(opts.Id) as OptOutCount
INTO #optouts
FROM autoprice.VehicleOptOuts opts --2871
INNER JOIN autoprice.VehicleAdverts ads on ads.id = opts.VehicleAdvert_Id
LEFT JOIN autoprice.RetailerSites rs on rs.Id = ads.RetailerSite_Id
INNER JOIN autoprice.VehicleAdvertSnapshots snaps on snaps.VehicleAdvert_Id = ads.id and snaps.SnapshotDate >  @chosenDate
WHERE CONVERT(date,opts.ActualEndDate) > CONVERT(date,@chosenDate) AND CONVERT(date,opts.CreatedDate) <= CONVERT(date,@chosenDate) 
AND rs.DealerGroup_Id = @dealerGroupId
AND snaps.LifecycleStatus <> 'WASTEBIN'
AND snaps.LifecycleStatus <> 'SOLD'
AND
(
	(ads.OwnershipCondition = 'New' AND @includeNewVehicles = 1)
	OR
	(ads.OwnershipCondition = 'Used' AND @includeUsedVehicles = 1)
)
GROUP BY ads.RetailerSite_Id



----------------------------------------------
--Sub table for most recent PriceRuleSets
----------------------------------------------

--SELECT
--	p.Name as CreatedByName,
--	sets.EffectiveFrom,
--	RetailerSite_Id as RetailerSiteId,
--	ROW_NUMBER() OVER (PARTITION BY RetailerSite_Id ORDER BY EffectiveFrom desc) as PricingRuleSetPrecedence
--INTO #allRunningPriceRuleSets
--FROM autoprice.PricingRuleSets sets
--INNER JOIN People p on p.Id = sets.Person_Id
--WHERE sets.EffectiveFrom < @chosenDate
--AND p.DealerGroup_Id = @dealerGroupId


--SELECT
--RetailerSiteId,CreatedByName,EffectiveFrom
--INTO #mostRecentPriceRuleSets
--FROM #allRunningPriceRuleSets
--WHERE PricingRuleSetPrecedence = 1


--DROP TABLE #allRunningPriceRuleSets


----------------------------------------------
--Result table 
--------------------------------------------

SELECT
retailerSites.Name,
retailerSites.Id as RetailerSiteId,
listed.VehiclesCount as ListedCount,
--sets.CreatedByName as PriceRuleSetCreatedBy,
--sets.EffectiveFrom as PriceRuleSetEffectiveDate,
pricechanges.AutoPriceChanges,
pricechanges.ApprovedCount,
retRatings.[not available] as NoRating,
retRatings.u20 as RetailRating_u20,
retRatings.u40 as RetailRating_u40,
retRatings.u60 as RetailRating_u60,
retRatings.u80 as RetailRating_u80,
retRatings.o80 as RetailRating_o80,
optouts.OptOutCount,
pricechanges.OptedOutOnDay as AutoPriceChangesOptedOutOnDay,
s.Id as SiteId
FROM autoprice.RetailerSites retailerSites
--LEFT JOIN #mostRecentPriceRuleSets sets on sets.RetailerSiteId = retailerSites.id
LEFT JOIN #autopricechangestoday pricechanges on pricechanges.RetailerSite_Id = retailerSites.id
LEFT JOIN #vehiclesByRetailRating retRatings on retRatings.RetailerSiteId = retailerSites.Id
LEFT JOIN #optouts optouts on optouts.RetailerSiteId = retailerSites.id
LEFT JOIN #vehiclesListed listed on listed.RetailerSiteId = retailerSites.Id
LEFT JOIN Sites s on s.Id = retailerSites.Site_Id
LEFT JOIN #eligibleSites es on es.Id = s.Id
WHERE es.Id IS NOT NULL OR @userEligibleSites IS NULL
AND retailerSites.DealerGroup_Id = @dealerGroupId
ORDER BY s.SortOrder



--------------------------------------------
-- Cleanup
--------------------------------------------
DROP TABLE #autopricechangestoday
DROP TABLE #vehiclesByRetailRating
DROP TABLE #optouts
DROP TABLE #vehiclesListed
DROP TABLE #eligibleSites
DROP TABLE #veryLatestRetailRatingsPerAd


END
GO


