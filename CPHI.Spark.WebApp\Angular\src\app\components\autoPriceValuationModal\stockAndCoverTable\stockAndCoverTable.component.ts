import { Component, Input, OnInit } from '@angular/core';
import { GridApi, GridOptions, GridReadyEvent } from 'ag-grid-community';
import { CphPipe } from 'src/app/cph.pipe';
import { SameModelAdvert } from 'src/app/model/SameModelAdvert';
import { AutoTraderAdvertImage } from 'src/app/_cellRenderers/autoTraderAdvertImage.component';
import { ColumnTypesService } from 'src/app/services/columnTypes.service';
import { VehicleValuationService } from '../vehicleValuation.service';
import { AGGridMethodsService } from 'src/app/services/agGridMethods.service';
import { AutoPriceInsightsModalService } from '../../autoPriceInsightsModal/autoPriceInsightsModal.service';

@Component({
  selector: 'stockAndCoverTable',
  templateUrl: './stockAndCoverTable.component.html',
  styleUrls: ['./stockAndCoverTable.component.scss']
})
export class StockAndCoverComponent implements OnInit {
  @Input() fullHeight: boolean;
  gridOptions: GridOptions;
  gridApi: GridApi;
  resizeObserver: ResizeObserver;

  constructor(
    public cphPipe: CphPipe,
    private colTypesService:ColumnTypesService,
    private valuationService: VehicleValuationService,
    private insightModalService: AutoPriceInsightsModalService,
    public gridHelpersService: AGGridMethodsService
  ) { }

  ngOnInit(): void {
    this.setGridDefinitions();

    const table = document.getElementById('stockAndCoverTable');

    this.resizeObserver = new ResizeObserver(entries => {
      if (this.gridApi) {
        setTimeout(() => {
          this.gridApi.sizeColumnsToFit();
        }, 250)
      }
    });

    if (table) {
      this.resizeObserver.observe(table);
    }
  }


  ngOnDestroy(): void {
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
  }


  setGridDefinitions() {
    this.gridOptions = {
      defaultColDef: {
        resizable: true,
        sortable: true,
        filterParams: {
          applyButton: false,
          clearButton: true,
          cellHeight: this.gridHelpersService.getFilterListItemHeight()
        },
        autoHeight: true,
        headerComponentParams: {
          showPinAndRemoveOptions: false
        },
        autoHeaderHeight: true
      },
      columnTypes: {...this.colTypesService.provideColTypes([])} ,
      rowSelection: 'multiple',

      rowData: this.valuationService.valuationModalResultNew ? this.valuationService.valuationModalResultNew.StockLevelAndCover.filter(x=>!x.IsTotal) 
                                                             : this.insightModalService.stockLevelAndCover.filter(x=>!x.IsTotal),
                                                             
      pinnedTopRowData: this.valuationService.valuationModalResultNew ? this.valuationService.valuationModalResultNew.StockLevelAndCover.filter(x=>x.IsTotal) 
                                                                      : this.insightModalService.stockLevelAndCover.filter(x=>x.IsTotal),
      columnDefs: [
        { headerName: 'Site', colId: 'RetailerName', field: 'RetailerName', type: 'label', width: 75 },
        { headerName: 'Stock Level', colId: 'StockCount',  field: 'StockCount', type: 'number', width: 30 },
        { headerName: 'Sales / month', colId: 'SalesPerMonth',  field: 'SalesPerMonth', type: 'number', width: 30 },
        { headerName: 'Stock cover', colId: 'Cover',  field: 'Cover', type: 'days', width: 30 },
      ],
      onGridReady: (event: GridReadyEvent) => this.onGridReady(event),
      getRowHeight: (params) => {
        if (params.node.rowPinned) {
          return this.gridHelpersService.getRowPinnedHeight();
        } else {
          return this.gridHelpersService.getStandardHeight();
        }
      }
    }
  }

  onGridReady(event: GridReadyEvent) {
    this.gridApi = event.api;
    this.gridApi.sizeColumnsToFit();
  }
}
