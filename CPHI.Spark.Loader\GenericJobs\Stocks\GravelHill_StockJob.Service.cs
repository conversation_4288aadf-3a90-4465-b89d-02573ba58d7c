using CPHI.Spark.Loader.Services.GenericStockLoader;
using CPHI.Spark.Model;
using CPHI.Spark.Model.Services;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using Microsoft.IdentityModel.Tokens;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;

namespace CPHI.Spark.Loader.Services.GenericStockLoader
{
   public class GravelHill_StockJobService : IGenericStockJobServiceParams
   {
      public int RowsToSkip { get; set; }
      public bool IncludePreviousUses { get; set; }
      public bool IncludeAccountStatuses { get; set; }
      public DealerGroupName DealerGroupName { get; set; }
      public string FileExt { get; set; }
      public string DbConnectionName { get; set; }
      public string DbConnectionString { get; set; }
      public string[] AllMatchingFiles { get; set; }
      public string FinalSPToRun { get; set; }
      public bool TriggerUpdate { get; set; }
      public string JobName { get; set; }
      public string? TabName { get; set; }
      public string OnlyForSiteIds { get; set; }

      public GravelHill_StockJobService(string incomingRoot)
      {
         GetMatchingFilesAndImportParams(incomingRoot);
      }

      public void GetMatchingFilesAndImportParams(string incomingRoot)
      {
         DealerGroupName = DealerGroupName.GravelHillCars;
         RowsToSkip = 0;
         JobName = string.Concat(DealerGroupName.GravelHillCars.ToString(), "StocksJob");
         IncludePreviousUses = false;
         IncludeAccountStatuses = false;
         FileExt = ".csv";
         DbConnectionString = ConfigService.autopriceConnectionString;
         DbConnectionName = "AutoPriceConnection";
         AllMatchingFiles = Directory.GetFiles(incomingRoot, $"*Stock Report - Management*.csv*")
                         .ToArray();
         TriggerUpdate = true;
      }

      public DataTable ConvertToDataTable(List<Stock> toReturn)
      {
         var dataTable = toReturn.ToDataTable();

         dataTable.Columns.Remove("PreviousSite");
         dataTable.Columns.Remove("Site");
         dataTable.Columns.Remove("DisposalRoute");
         dataTable.Columns.Remove("ProgressCode");
         dataTable.Columns.Remove("VehicleType");
         dataTable.Columns.Remove("AccountStatus");
         dataTable.Columns.Remove("PreviousUse");
         dataTable.Columns.Remove("VehicleAdvert");

         return dataTable;
      }

      public Stock ConvertRowToStock(List<string> rowCells, GenericStockLoaderDbLookups lookups, Dictionary<string, int> headerDictionary)
      {
         Stock newRow = new Stock();

         // Selling Price
         newRow.Selling = RowInterpretationService.GetDecimal(rowCells[headerDictionary["SELLING PRICE"]]);

         // Stock number
         string stockNumberStr = rowCells[headerDictionary["STOCK NUMBER"]];

         if (int.TryParse(Regex.Replace(stockNumberStr, "[^0-9]", ""), out int stockNumber))
         {
            newRow.StockNumber = stockNumber;
         }

         newRow.StockNumberFull = stockNumberStr;
         newRow.UniqueId = stockNumberStr;

         // Registration
         newRow.Reg = rowCells[headerDictionary["REGISTRATION"]];
         newRow.Make = rowCells[headerDictionary["MAKE"]];
         newRow.Model = rowCells[headerDictionary["MODEL"]];

         // Description - limit to 50 characters
         string desc = rowCells[headerDictionary["DESCRIPTION"]];
         if (desc.Length > 50)
         {
            desc = desc.Substring(0, 50);
         }
         newRow.Description = desc;

         // Colour and Chassis
         newRow.Colour = rowCells[headerDictionary["COLOUR"]];
         newRow.Chassis = rowCells[headerDictionary["CHASSIS NO"]];

         // Mileage
         if (int.TryParse(rowCells[headerDictionary["MILEAGE"]], out int mileage))
         {
            newRow.Mileage = mileage;
         }

         // Registration Date - validate SQL DateTime range
         DateTime? regDate = RowInterpretationService.GetNullableDateFromString(rowCells[headerDictionary["DATE REGISTERED"]]);

         if (regDate.HasValue && IsValidSqlDateTime(regDate.Value))
         {
            newRow.RegDate = regDate;
         }
         else
         {
            newRow.RegDate = null;
         }

         newRow.Dis = RowInterpretationService.GetInt(rowCells[headerDictionary["DAYS IN STOCK"]]);

         newRow.StockDate = DateTime.Now.AddDays(newRow.Dis * -1);
         newRow.BranchStockDate = DateTime.Now.AddDays(newRow.Dis * -1);

         // Purchase Price
         decimal purchasePrice = RowInterpretationService.GetDecimal(rowCells[headerDictionary["PURCHASE PRICE"]]);
         newRow.OriginalPurchasePrice = purchasePrice;
         newRow.Purchased = purchasePrice;

         // Stand In Value (SIV)
         newRow.Siv = RowInterpretationService.GetDecimal(rowCells[headerDictionary["STAND IN VALUE"]]);

         // Non Recoverable Costs - using Costs Total
         // decimal costsTotal = RowInterpretationService.GetDecimal(rowCells[headerDictionary["COSTS TOTAL"]]);
         // newRow.NonRecoverableCosts = (float)costsTotal;

         // Only one site
         newRow.Site_Id = 299;

         string vehType = rowCells[headerDictionary["SALES STATUS"]]; 

         // Either SOR or USED
         if(vehType == "SOR")
         {
            newRow.VehicleType_Id = lookups.vehicleTypes.First(x => x.Description == "SOR").Id;
         }
         else
         {
            newRow.VehicleType_Id = lookups.vehicleTypes.First(x => x.Description == "Used Vehicles").Id;
         }

         // VAT Qualifying
         newRow.IsVatQ = rowCells[headerDictionary["PURCHASE TYPE GROUP"]] == "Margin Scheme Vehicles" ? false : true;

         newRow.LastUpdatedDate = DateTime.Now;

         return newRow;
      }

      public Dictionary<string, int> BuildHeaderDictionary(List<string> headers)
      {
         Dictionary<string, int> result = new Dictionary<string, int>
            {
                { "STOCK TYPE", headers.IndexOf("STOCK TYPE") },
                { "STOCK NUMBER", headers.IndexOf("STOCK NUMBER") },
                { "REGISTRATION", headers.IndexOf("REGISTRATION") },
                { "MAKE", headers.IndexOf("MAKE") },
                { "DESCRIPTION", headers.IndexOf("DESCRIPTION") },
                { "COLOUR", headers.IndexOf("COLOUR") },
                { "MILEAGE", headers.IndexOf("MILEAGE") },
                { "DATE REGISTERED", headers.IndexOf("DATE REGISTERED") },
                { "SALES STATUS", headers.IndexOf("SALES STATUS") },
                { "DAYS IN STOCK", headers.IndexOf("DAYS IN STOCK") },
                { "DATEDUEIN", headers.IndexOf("DATEDUEIN") },
                { "PURCHASE PRICE", headers.IndexOf("PURCHASE PRICE") },
                { "COSTS TOTAL", headers.IndexOf("COSTS TOTAL") },
                { "WRITE DOWN TOTAL", headers.IndexOf("WRITE DOWN TOTAL") },
                { "STAND IN VALUE", headers.IndexOf("STAND IN VALUE") },
                { "PART EXCHANGE OVER ALLOWANCE", headers.IndexOf("PART EXCHANGE OVER ALLOWANCE") },
                { "PURCHASE OVER ALLOWANCE TOTAL", headers.IndexOf("PURCHASE OVER ALLOWANCE TOTAL") },
                { "MARGIN VAT", headers.IndexOf("MARGIN VAT") },
                { "TOTALPURCHASEIPT", headers.IndexOf("TOTALPURCHASEIPT") },
                { "PURCHASE TYPE FLAG", headers.IndexOf("PURCHASE TYPE FLAG") },
                { "SELLING PRICE", headers.IndexOf("SELLING PRICE") },
                { "MARGIN", headers.IndexOf("MARGIN") },
                { "TDA", headers.IndexOf("TDA") },
                { "LOCATION", headers.IndexOf("LOCATION") },
                { "PARKINGLOCATION", headers.IndexOf("PARKINGLOCATION") },
                { "PURCHASE TYPE GROUP", headers.IndexOf("PURCHASE TYPE GROUP") },
                { "PURCHASE INVOICED", headers.IndexOf("PURCHASE INVOICED") },
                { "MODEL", headers.IndexOf("MODEL") },
                { "AGREED DELIVERY", headers.IndexOf("AGREED DELIVERY") },
                { "KEY NUMBER", headers.IndexOf("KEY NUMBER") },
                { "TOTALNETPRICE", headers.IndexOf("TOTALNETPRICE") },
                { "TOTALNETMARGIN", headers.IndexOf("TOTALNETMARGIN") },
                { "FUEL TYPE", headers.IndexOf("FUEL TYPE") },
                { "TRANSMISSION", headers.IndexOf("TRANSMISSION") },
                { "ENGINE SIZE", headers.IndexOf("ENGINE SIZE") },
                { "DOORS", headers.IndexOf("DOORS") },
                { "BODY STYLE", headers.IndexOf("BODY STYLE") },
                { "PURCHASED BY", headers.IndexOf("PURCHASED BY") },
                { "CHASSIS NO", headers.IndexOf("CHASSIS NO") },
                { "V5 REFERENCE", headers.IndexOf("V5 REFERENCE") },
                { "BRANCH", headers.IndexOf("BRANCH") },
                { "BONUS TOTAL", headers.IndexOf("BONUS TOTAL") },
                { "SUPPLIER NAME", headers.IndexOf("SUPPLIER NAME") },
                { "ADVERTPRICE", headers.IndexOf("ADVERTPRICE") },
                { "PRICELASTCHANGED", headers.IndexOf("PRICELASTCHANGED") },
                { "COUNTRY OF ORIGIN", headers.IndexOf("COUNTRY OF ORIGIN") },
                { "GROSS COMBINED WEIGHT", headers.IndexOf("GROSS COMBINED WEIGHT") },
                { "PURCHASE INVOICE NUMBER", headers.IndexOf("PURCHASE INVOICE NUMBER") },
                { "PAYMENTSCHEME", headers.IndexOf("PAYMENTSCHEME") },
                { "BNIEXPORT", headers.IndexOf("BNIEXPORT") },
                { "VEHICLEGROUPPURCHASE", headers.IndexOf("VEHICLEGROUPPURCHASE") },
                { "VEHICLEGROUPSALE", headers.IndexOf("VEHICLEGROUPSALE") },
                { "PURCHASING STATUS_CF", headers.IndexOf("PURCHASING STATUS_CF") }
            };

         return result;
      }

      // Reinstate this if necessary -
      // Do not load any stock against these vehicle types
      // public bool IsInvalidVehType(string input)
      // {
      //     var recognisedTypes = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
      //     {
      //         "AUCTION",
      //         "COMPANY",
      //         "PARTSVAN",
      //         "RUNAROUND",
      //         "TRADE"
      //     };

      //     return recognisedTypes.Contains(input?.Trim());
      // }

      // Validate that DateTime is within SQL Server DateTime range
      private bool IsValidSqlDateTime(DateTime dateTime)
      {
         // SQL Server DateTime range: 1753-01-01 to 9999-12-31
         DateTime sqlMinDate = new DateTime(1753, 1, 1);
         DateTime sqlMaxDate = new DateTime(9999, 12, 31, 23, 59, 59);

         return dateTime >= sqlMinDate && dateTime <= sqlMaxDate;
      }
   }
}
