﻿using CPHI.Spark.Model;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using Newtonsoft.Json;
using System.Net.Http.Headers;
using System.Text;
using System.Collections.Concurrent;
using log4net;
using CPHI.Spark.Model.AutoPrice;
using Microsoft.IdentityModel.Tokens;
using System.Threading.RateLimiting;
using CPHI.Spark.DataAccess.DataAccess.AutoPrice;
using System.Diagnostics;
using System.Threading;



namespace CPHI.Spark.DataAccess.AutoPrice
{

   public interface IAutoTraderVehiclesClient
   {
      Task<ATNewVehicleGet> GetIndividualVehicle(string registration, int mileage, int advertiserId, string token, string autotraderBaseURL);
      Task<ATNewVehicleGet> GetIndividualVehicleForOwnerAndColour(string registration, int mileage, int advertiserId, string token, string autotraderBaseURL);
   }



   public class AutoTraderVehiclesClient : IAutoTraderVehiclesClient
   {

      private readonly HttpClient _httpClient;
      private string atApiKey;
      private string atApiSecret;
      private string atBaseURL;
      private readonly IAutoTraderApiTokenClient autoTraderApiTokenClient;
      private readonly RateLimiter rateLimiter;



      public AutoTraderVehiclesClient(IHttpClientFactory httpClientFactory, string atApiKeyIn, string atApiSecretIn, string atBaseURLIn) //, FixedWindowRateLimiter rateLimiterIn
      {
         _httpClient = httpClientFactory.CreateClient();

         atApiKey = atApiKeyIn;
         atApiSecret = atApiSecretIn;
         atBaseURL = atBaseURLIn;

         autoTraderApiTokenClient = new AutoTraderApiTokenClient(httpClientFactory, atApiKey, atApiSecret, atBaseURL);
         rateLimiter = AutotraderRateLimiter.VehiclesLimiter;
      }








      //------------------------------------------------------------------
      #region Vehicles
      //------------------------------------------------------------------


      public async Task<ATNewVehicleGet> GetIndividualVehicleForOwnerAndColour(string registration, int mileage, int advertiserId, string token, string autotraderBaseURL)
      {
         string url = $@"{autotraderBaseURL}/vehicles?registration={registration}"
                + $"&advertiserId={RetailerIdSwapService.ProvideUpdatedId(advertiserId)}"
                + $"&odometerReadingMiles={mileage}";

         var request = new HttpRequestMessage(HttpMethod.Get, url);
         request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", token);

         var response = await SendRateLimitedRequestAsync(request);

         if (response.IsSuccessStatusCode)
         {
            var responseContent = await response.Content.ReadAsStringAsync();
            try
            {
               ATNewVehicleGet vehicleMetricAPIResponse = JsonConvert.DeserializeObject<ATNewVehicleGet>(responseContent);
               vehicleMetricAPIResponse.errorMessage = string.Empty;
               return vehicleMetricAPIResponse;
            }
            catch (Exception ex)
            {
               throw;
               //logger?.Error($"Failed when trying url {url}.   Status: {response.StatusCode}.   Exception: {ex.Message}");
            }
         }

         throw new Exception($"Unable to retrieve data: {response.StatusCode}");
         //logger?.Error($"Failed when trying url {url}.   Status: {response.StatusCode} ");
         //return null;
      }

      public async Task<ATNewVehicleGet> GetIndividualVehicle(string registration, int mileage, int advertiserId, string token, string autotraderBaseURL)
      {
         string url = $"{autotraderBaseURL}/vehicles" +
             $"?registration={registration}" +
             $"&advertiserId={RetailerIdSwapService.ProvideUpdatedId(advertiserId)}" +
             $"&odometerReadingMiles={mileage}" +
             "&valuations=true" +
             "&vehicleMetrics=true" +
             "&features=true" +
             "&history=true" +
             "&motTests=true" +
             "&competitors=true"
             ;

         var request = new HttpRequestMessage(HttpMethod.Get, url);
         request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", token);

         var response = await SendRateLimitedRequestAsync(request);

         if (response.IsSuccessStatusCode)
         {
            var responseContent = await response.Content.ReadAsStringAsync();
            try
            {

               ATNewVehicleGet vehicleMetricAPIResponse = JsonConvert.DeserializeObject<ATNewVehicleGet>(responseContent);

               if (vehicleMetricAPIResponse.history?.yearOfManufacture == null) { vehicleMetricAPIResponse.history.yearOfManufacture = vehicleMetricAPIResponse.vehicle.firstRegistrationDate.Year; }

               //vehicleMetricAPIResponse.vehicle.ageAndOwners = BandingsService.ProvideAgeAndOwners((DateTime.Now - vehicleMetricAPIResponse.vehicle.firstRegistrationDate).Days / 365, vehicleMetricAPIResponse.vehicle.owners);


               vehicleMetricAPIResponse.errorMessage = string.Empty;
               return vehicleMetricAPIResponse;
            }
            catch (Exception ex)
            {
               return BuildErrorResult(registration, null);

            }
         }

         // Add error message if the call failed
         if(!response.IsSuccessStatusCode)
         {
            return BuildErrorResult(registration, response.ReasonPhrase);
         }

         // We shouldn't ever get here - requires a return for this code path
         throw new Exception($"Unable to retrieve data: {response.StatusCode} reg: {registration} mileage: {mileage}");
      }

      #endregion

      // Add additional reason phrases here as necessary
      private ATNewVehicleGet BuildErrorResult(string registration, string? reasonPhrase)
      {
         string message;

         if (reasonPhrase == "Not Found")
         {
            message = $"Unable to find {registration} on AutoTrader. Vehicle may be too new to be valued yet.";
         }
         else
         {
            message = $"Unable to value {registration} due to unknown error.";
         }

         return new ATNewVehicleGet
         {
            errorMessage = message
         };
      }

      private async Task<HttpResponseMessage> SendRateLimitedRequestAsync(HttpRequestMessage request, CancellationToken cancellationToken = default) // Add CancellationToken if needed
      {
         using (RateLimitLease lease = await rateLimiter.AcquireAsync(1, cancellationToken))
         {
            if (!lease.IsAcquired)
            {
               // Handle rejection (e.g., throw RateLimiterRejectedException)
               throw new TimeoutException($"Rate limit permit could not be acquired for request to {request.RequestUri}.");
            }
            // Only send if lease was acquired
            return await _httpClient.SendAsync(request, cancellationToken);
         }
      }















   }
}
