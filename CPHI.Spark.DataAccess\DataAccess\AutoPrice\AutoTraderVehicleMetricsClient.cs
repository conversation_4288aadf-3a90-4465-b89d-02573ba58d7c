﻿using Azure;
using CPHI.Spark.DataAccess.DataAccess.AutoPrice;
using CPHI.Spark.Model;
using CPHI.Spark.Model.AutoPrice;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using log4net;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json;
using System.Collections.Concurrent;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.RateLimiting;



namespace CPHI.Spark.DataAccess.AutoPrice
{

   public interface IAutoTraderVehicleMetricsClient
   {
      Task<decimal> GetAdvertPriceAdjustedDaysToSell(GetAdvertPriceAdjustedDaysToSellParams parms, string _bearerToken, ILog logger);
      Task<VehicleMetricAPI_Location> GetAdvertPriceAdjustedDaysToSellAndRating(GetAdvertPriceAdjustedDaysToSellParams parms, string _bearerToken);
      Task<List<LocationDaysToSellAndRetailRating>> GetAdvertPriceAdjustedDaysToSellMultiSite(GetAdvertPriceAdjustedDaysToSellMultiSiteParams parms, string _bearerToken);
      //Task<List<VehicleMetricAPIResponse>> GetAdvertsWithRetailRatingAtMultipleLocations(string apiKey, string apiSecret, List<VehicleMetricAPIParam> vehicleMetricAPIParams, string autotraderBaseURL, TokenResponse _bearerToken);
      //Task<List<VehicleMetricAPIResponseBasedOnLongLat>> GetAdvertsWithRetailRatingAtMultipleLocationsBasedOnLongLat(string apiKey, string apiSecret, List<VehicleMetricAPIParamBasedOnLongLat> vehicleMetricAPIParams, string autotraderBaseURL, TokenResponse _bearerToken);
      Task<List<VehicleMetricProcessedResponse>> GetAdvertsWithRetailRatingAtMultipleLocationsNEW(List<VehicleMetricParams> vehicleMetricAPIParams, string autotraderBaseURL, TokenResponse _bearerToken);
      Task<VehicleMetricProcessedLocation> GetRetailRatingAndDTSAtSpecificLongLat(decimal longitude, decimal latitude, string derivativeId, int odometer, DateTime firstRegDate, int advertiserId, TokenResponse _bearerToken, string autotraderBaseURL);
   }



   public class AutoTraderVehicleMetricsClient : IAutoTraderVehicleMetricsClient
   {

      private readonly HttpClient _httpClient;
      private string atApiKey;
      private string atApiSecret;
      private string atBaseURL;
      private readonly IAutoTraderApiTokenClient autoTraderApiTokenClient;
      private readonly RateLimiter rateLimiter;



      public AutoTraderVehicleMetricsClient(IHttpClientFactory httpClientFactory, string atApiKeyIn, string atApiSecretIn, string atBaseURLIn) //, FixedWindowRateLimiter rateLimiterIn
      {
         _httpClient = httpClientFactory.CreateClient();

         atApiKey = atApiKeyIn;
         atApiSecret = atApiSecretIn;
         atBaseURL = atBaseURLIn;

         autoTraderApiTokenClient = new AutoTraderApiTokenClient(httpClientFactory, atApiKey, atApiSecret, atBaseURL);
         rateLimiter = AutotraderRateLimiter.VehicleMetricsLimiter;
      }




      //------------------------------------------------------------------
      #region Vehicle Metrics
      //------------------------------------------------------------------

      public async Task<decimal> GetAdvertPriceAdjustedDaysToSell(GetAdvertPriceAdjustedDaysToSellParams parms, string _bearerToken, ILog logger)
      {
         try
         {

            GetAdvertPriceAdjustedDaysToSellMultiSiteParams multiSiteParams = new GetAdvertPriceAdjustedDaysToSellMultiSiteParams(parms);

            // Defer the multi-site call
            List<LocationDaysToSellAndRetailRating> results = await GetAdvertPriceAdjustedDaysToSellMultiSite(multiSiteParams, _bearerToken);

            // Check if the results list is not empty and return the DaysToSell of the first item
            if (results != null && results.Count > 0)
            {
               return results[0].DaysToSell;
            }

            // If no results were returned, handle it (you could return a default value or an error message)
            return 0;

         }
         catch (Exception ex)
         {
            if (logger != null)
            {
               logger.Error(ex.Message);
            }
            return 0;
         }
      }


      public async Task<List<LocationDaysToSellAndRetailRating>> GetAdvertPriceAdjustedDaysToSellMultiSite(GetAdvertPriceAdjustedDaysToSellMultiSiteParams parms, string _bearerToken)
      {
         //try
         //{

         List<int> advertiserIds = parms.AdvertiserIds.Select(x => RetailerIdSwapService.ProvideUpdatedId(x)).Distinct().ToList();
         string url = $"{parms.AutotraderBaseURL}/vehicle-metrics?advertiserId={advertiserIds[0]}";
         var request = new HttpRequestMessage(HttpMethod.Post, url);
         GetAdvertPriceAdjustedDaysToSellAtParams payload = new GetAdvertPriceAdjustedDaysToSellAtParams()
         {
            vehicle = new VehicleMetricAPI_Vehicle()
            {
               derivativeId = parms.DerivativeId,
               firstRegistrationDate = parms.FirstRegistrationDate != null ? ((DateTime)(parms.FirstRegistrationDate)).ToString("yyyy-MM-dd") : DateTime.Now.ToString("yyyy-MM-dd"),
               odometerReadingMiles = parms.OdometerReadingMiles
            },
            adverts = new VehicleMetricAPI_Adverts() { retailAdverts = new VehicleMetricAPI_RetailAdverts() { price = new ATNewVehicleGet_Valuation() { amountGBP = parms.Amount } } },
            locations = advertiserIds.Select(x => new VehicleMetricAPI_Location() { advertiserId = x.ToString() }).ToList()
         };

         if (parms.UseSpecificOptions)
         {
            payload.features = parms.SpecificOptionNames.Select(x => new ValuationAPI_Feature() { name = x }).ToList();
         }


         string jsonPayload = System.Text.Json.JsonSerializer.Serialize(payload);
         request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", _bearerToken);
         request.Content = new StringContent(jsonPayload, Encoding.UTF8, "application/json");
         var response = await SendRateLimitedRequestAsync(request);

         if (response.IsSuccessStatusCode)
         {
            var responseContent = await response.Content.ReadAsStringAsync();
            var jsonResult = responseContent;


            VehicleMetricAPIResponse vehicleMetricAPIResponse = System.Text.Json.JsonSerializer.Deserialize<VehicleMetricAPIResponse>(jsonResult);
            if (vehicleMetricAPIResponse?.vehicleMetrics?.retail?.locations == null)
            {
               return new List<LocationDaysToSellAndRetailRating>();
            }


            List<VehicleMetricAPI_Location>? locations = vehicleMetricAPIResponse.vehicleMetrics.retail?.locations;
            return locations.Select(x => new LocationDaysToSellAndRetailRating(x)).ToList();

         }
         else
         {
            var responseContent = await response.Content.ReadAsStringAsync();
            string error = $"GetAdvertPriceAdjustedDaysToSell | Request: URL={request.RequestUri},  | Response: StatusCode={response.StatusCode}, Content={responseContent}";
            return null;
            //throw new Exception(error);
         }
         //}
         //catch (Exception ex)
         //{

         //   return null;
         //}
      }



      public async Task<VehicleMetricAPI_Location> GetAdvertPriceAdjustedDaysToSellAndRating(GetAdvertPriceAdjustedDaysToSellParams parms, string _bearerToken)
      {
         ValidateParams(parms);

         string url = $"{parms.AutotraderBaseURL}/vehicle-metrics?advertiserId={RetailerIdSwapService.ProvideUpdatedId(parms.AdvertiserId)}";
         var request = new HttpRequestMessage(HttpMethod.Post, url);


         GetAdvertPriceAdjustedDaysToSellAtParams payload = new GetAdvertPriceAdjustedDaysToSellAtParams()
         {
            vehicle = new VehicleMetricAPI_Vehicle()
            {
               derivativeId = parms.DerivativeId,
               firstRegistrationDate = parms.FirstRegistrationDate != null ? ((DateTime)(parms.FirstRegistrationDate)).ToString("yyyy-MM-dd") : DateTime.Now.ToString("yyyy-MM-dd"),
               odometerReadingMiles = parms.OdometerReadingMiles
            },
            adverts = new VehicleMetricAPI_Adverts() { retailAdverts = new VehicleMetricAPI_RetailAdverts() { price = new ATNewVehicleGet_Valuation() { amountGBP = parms.Amount } } }
         };

         if (parms.UseSpecificOptions)
         {
            payload.features = parms.SpecificOptionNames.Select(x => new ValuationAPI_Feature() { name = x }).ToList();
         }


         string jsonPayload = System.Text.Json.JsonSerializer.Serialize(payload);
         request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", _bearerToken);
         request.Content = new StringContent(jsonPayload, Encoding.UTF8, "application/json");
         var response = await SendRateLimitedRequestAsync(request);

         if (response.IsSuccessStatusCode)
         {
            var responseContent = await response.Content.ReadAsStringAsync();
            var jsonResult = responseContent;
            try
            {

               VehicleMetricAPIResponse vehicleMetricAPIResponse = System.Text.Json.JsonSerializer.Deserialize<VehicleMetricAPIResponse>(jsonResult);
               return new VehicleMetricAPI_Location()
               {
                  advertiserId = parms.AdvertiserId.ToString(),
                  daysToSell = vehicleMetricAPIResponse.vehicleMetrics.retail.daysToSell,
                  rating = vehicleMetricAPIResponse.vehicleMetrics.retail.rating
               };
            }
            catch (Exception ex)
            {
               { }
               throw new Exception(ex.Message, ex);
            }
         }
         else
         {
            //throw new Exception($"Unable to retrieve data: {response.StatusCode}");
            var responseContent = await response.Content.ReadAsStringAsync();
            string error = $"GetAdvertPriceAdjustedDaysToSellAndRating | Request: URL={request.RequestUri}, Body={jsonPayload} | Response: StatusCode={response.StatusCode}, Headers={string.Join(", ", response.Headers.Select(h => $"{h.Key}={string.Join(",", h.Value)}"))}, Body={responseContent}";
            throw new Exception(error);
         }
      }

      public async Task<List<VehicleMetricProcessedResponse>> GetAdvertsWithRetailRatingAtMultipleLocationsNEW(
         List<VehicleMetricParams> vehicleMetricAPIParams,
         string autotraderBaseURL,
         TokenResponse _bearerToken)
      {
         List<VehicleMetricProcessedResponse> vehicleMetricAPIResponses = new List<VehicleMetricProcessedResponse>();

         //We were not making API calls in batches, so commented the above.
         /*
         //Split the request
         int batchSize = vehicleMetricAPIParams.Count() >= 25 ? vehicleMetricAPIParams.Count() / 25 : 1;


         List<List<VehicleMetricParams>> paramsBatches = vehicleMetricAPIParams
             .Select((item, index) => new { item, index })
             .GroupBy(x => x.index / batchSize)
             .Select(group => group.Select(x => x.item).ToList())
             .ToList();
         */


         foreach (var paramsBatch in vehicleMetricAPIParams)
         {
            await GetAdvertsWithRetailRatingAtMultipleLocationsBatches(paramsBatch, autotraderBaseURL, _bearerToken, vehicleMetricAPIResponses);
         }


         return vehicleMetricAPIResponses;
      }



      //public async Task<List<VehicleMetricAPIResponse>> GetAdvertsWithRetailRatingAtMultipleLocations(
      //   string apiKey,
      //   string apiSecret,
      //   List<VehicleMetricAPIParam> vehicleMetricAPIParams,
      //   string autotraderBaseURL,
      //   TokenResponse _bearerToken)
      //{
      //   List<VehicleMetricAPIResponse> vehicleMetricAPIResponses = new List<VehicleMetricAPIResponse>();

      //   //Split the request
      //   int batchSize = vehicleMetricAPIParams.Count() >= 25 ? vehicleMetricAPIParams.Count() / 25 : 1;


      //   List<List<VehicleMetricAPIParam>> paramsBatches = vehicleMetricAPIParams
      //       .Select((item, index) => new { item, index })
      //       .GroupBy(x => x.index / batchSize)
      //       .Select(group => group.Select(x => x.item).ToList())
      //       .ToList();


      //   List<Task> tasks = new();
      //   foreach (var paramsBatch in paramsBatches)
      //   {
      //      tasks.Add(Task.Run(() => GetAdvertsWithRetailRatingAtMultipleLocationsAPICall(paramsBatch, autotraderBaseURL, _bearerToken, vehicleMetricAPIResponses)));
      //   }

      //   await Task.WhenAll(tasks);

      //   return vehicleMetricAPIResponses;
      //}

      //public async Task<List<VehicleMetricAPIResponseBasedOnLongLat>> GetAdvertsWithRetailRatingAtMultipleLocationsBasedOnLongLat(
      //  string apiKey,
      //  string apiSecret,
      //  List<VehicleMetricAPIParamBasedOnLongLat> vehicleMetricAPIParams,
      //  string autotraderBaseURL,
      //  TokenResponse _bearerToken)
      //{
      //   List<VehicleMetricAPIResponseBasedOnLongLat> vehicleMetricAPIResponses = new List<VehicleMetricAPIResponseBasedOnLongLat>();

      //   //Split the request
      //   int batchSize = vehicleMetricAPIParams.Count() >= 25 ? vehicleMetricAPIParams.Count() / 25 : 1;


      //   List<List<VehicleMetricAPIParamBasedOnLongLat>> paramsBatches = vehicleMetricAPIParams
      //       .Select((item, index) => new { item, index })
      //       .GroupBy(x => x.index / batchSize)
      //       .Select(group => group.Select(x => x.item).ToList())
      //       .ToList();


      //   List<Task> tasks = new();
      //   foreach (var paramsBatch in paramsBatches)
      //   {
      //      tasks.Add(Task.Run(() => GetAdvertsWithRetailRatingAtMultipleLocationsAPICallBasedOnLongLat(paramsBatch, autotraderBaseURL, _bearerToken, vehicleMetricAPIResponses)));
      //   }

      //   await Task.WhenAll(tasks);

      //   return vehicleMetricAPIResponses;
      //}

      //private async Task GetAdvertsWithRetailRatingAtMultipleLocationsAPICall(
      //  List<VehicleMetricAPIParam> vehicleMetricAPIParams,
      //  string autotraderBaseURL,
      //  TokenResponse _bearerToken,
      //  List<VehicleMetricAPIResponse> vehicleMetricAPIResponses)
      //{
      //   foreach (var vehicleMetricAPIParam in vehicleMetricAPIParams)
      //   {
      //      string url = $"{autotraderBaseURL}/vehicle-metrics?advertiserId={RetailerIdSwapService.ProvideUpdatedId(vehicleMetricAPIParam.advertiserId)}";
      //      var request = new HttpRequestMessage(HttpMethod.Post, url);
      //      string jsonPayload = System.Text.Json.JsonSerializer.Serialize(vehicleMetricAPIParam);

      //      _bearerToken = await autoTraderApiTokenClient.CheckExpiryAndRegenerate(_bearerToken);

      //      request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", _bearerToken.AccessToken);
      //      request.Content = new StringContent(jsonPayload, Encoding.UTF8, "application/json");

      //      var response = await SendRateLimitedRequestAsync(request);

      //      if (response.IsSuccessStatusCode)
      //      {
      //         var responseContent = await response.Content.ReadAsStringAsync();
      //         var jsonResult = responseContent;
      //         try
      //         {
      //            VehicleMetricAPIResponseError vehicleMetricAPIResponseError = System.Text.Json.JsonSerializer.Deserialize<VehicleMetricAPIResponseError>(jsonResult);
      //            if (vehicleMetricAPIResponseError?.messages?.FirstOrDefault()?.type == "ERROR")
      //            {
      //               vehicleMetricAPIResponses.Add(new VehicleMetricAPIResponse() { errorMessage = vehicleMetricAPIResponseError.messages.First().message, vehicleAdvertSnapshotId = vehicleMetricAPIParam.vehicleAdvertSnapshotId });
      //            }
      //            else
      //            {
      //               VehicleMetricAPIResponse vehicleMetricAPIResponse = System.Text.Json.JsonSerializer.Deserialize<VehicleMetricAPIResponse>(jsonResult);
      //               vehicleMetricAPIResponse.errorMessage = string.Empty;
      //               vehicleMetricAPIResponse.vehicleAdvertSnapshotId = vehicleMetricAPIParam.vehicleAdvertSnapshotId;
      //               vehicleMetricAPIResponses.Add(vehicleMetricAPIResponse);
      //            }
      //         }
      //         catch (Exception ex)
      //         {
      //            vehicleMetricAPIResponses.Add(new VehicleMetricAPIResponse() { errorMessage = ex.Message, vehicleAdvertSnapshotId = vehicleMetricAPIParam.vehicleAdvertSnapshotId });
      //         }
      //      }
      //      else
      //      {
      //         var responseContent = await response.Content.ReadAsStringAsync();

      //         string error = $"GetAdvertsWithRetailRatingAtMultipleLocationsAPICall | Request: URL={request.RequestUri}, Body={jsonPayload} | Response: StatusCode={response.StatusCode}, Headers={string.Join(", ", response.Headers.Select(h => $"{h.Key}={string.Join(",", h.Value)}"))}, Body={responseContent}";
      //         var errorMessage = $"Non success code on VehicleMetricAPIResponse {RetailerIdSwapService.ProvideUpdatedId(vehicleMetricAPIParam.advertiserId)}  | {vehicleMetricAPIParam.vehicle.derivativeId}.  Error is {error}";
      //         vehicleMetricAPIResponses.Add(new VehicleMetricAPIResponse() { errorMessage = errorMessage, vehicleAdvertSnapshotId = vehicleMetricAPIParam.vehicleAdvertSnapshotId });
      //      }
      //   }
      //}

      private static void ValidateParams(GetAdvertPriceAdjustedDaysToSellParams parms)
      {
         if (parms.Amount == 0)
         {
            throw new Exception("Value is Zero");
         }

         if (parms.AdjustedValuation != null && parms.AdjustedValuation != 0)
         {

            if (parms.Amount < (int)(0.9M * parms.AdjustedValuation))
            {
               throw new Exception("Value is below 90% of valuation");
            }

            if (parms.Amount > (int)(1.1M * parms.AdjustedValuation))
            {
               throw new Exception("Value is above 110% of valuation");
            }
         }

         if (parms.AverageValuation != null && parms.AverageValuation != 0)
         {

            if (parms.Amount < (int)(0.9M * parms.AverageValuation))
            {
               throw new Exception("Value is below 90% of valuation");
            }

            if (parms.Amount > (int)(1.1M * parms.AverageValuation))
            {
               throw new Exception("Value is above 110% of valuation");
            }
         }
      }

      //private async Task GetAdvertsWithRetailRatingAtMultipleLocationsAPICallBasedOnLongLat(
      //List<VehicleMetricAPIParamBasedOnLongLat> vehicleMetricAPIParams,
      //string autotraderBaseURL,
      //TokenResponse _bearerToken,
      // List<VehicleMetricAPIResponseBasedOnLongLat> vehicleMetricAPIResponses)
      //{
      //   foreach (var vehicleMetricAPIParam in vehicleMetricAPIParams)
      //   {
      //      string url = $"{autotraderBaseURL}/vehicle-metrics?advertiserId={RetailerIdSwapService.ProvideUpdatedId(vehicleMetricAPIParam.advertiserId)}";
      //      var request = new HttpRequestMessage(HttpMethod.Post, url);
      //      string jsonPayload = System.Text.Json.JsonSerializer.Serialize(vehicleMetricAPIParam);

      //      _bearerToken = await autoTraderApiTokenClient.CheckExpiryAndRegenerate(_bearerToken);

      //      request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", _bearerToken.AccessToken);
      //      request.Content = new StringContent(jsonPayload, Encoding.UTF8, "application/json");

      //      var response = await SendRateLimitedRequestAsync(request);

      //      if (response.IsSuccessStatusCode)
      //      {
      //         var responseContent = await response.Content.ReadAsStringAsync();
      //         var jsonResult = responseContent;
      //         try
      //         {
      //            VehicleMetricAPIResponseError vehicleMetricAPIResponseError = System.Text.Json.JsonSerializer.Deserialize<VehicleMetricAPIResponseError>(jsonResult);
      //            if (vehicleMetricAPIResponseError?.messages?.FirstOrDefault()?.type == "ERROR")
      //            {
      //               vehicleMetricAPIResponses.Add(new VehicleMetricAPIResponseBasedOnLongLat() { errorMessage = vehicleMetricAPIResponseError.messages.First().message, vehicleAdvertSnapshotId = vehicleMetricAPIParam.vehicleAdvertSnapshotId });
      //            }
      //            else
      //            {
      //               VehicleMetricAPIResponseBasedOnLongLat vehicleMetricAPIResponse = System.Text.Json.JsonSerializer.Deserialize<VehicleMetricAPIResponseBasedOnLongLat>(jsonResult);
      //               vehicleMetricAPIResponse.errorMessage = string.Empty;
      //               vehicleMetricAPIResponse.vehicleAdvertSnapshotId = vehicleMetricAPIParam.vehicleAdvertSnapshotId;
      //               vehicleMetricAPIResponses.Add(vehicleMetricAPIResponse);
      //            }
      //         }
      //         catch (Exception ex)
      //         {
      //            vehicleMetricAPIResponses.Add(new VehicleMetricAPIResponseBasedOnLongLat() { errorMessage = ex.Message, vehicleAdvertSnapshotId = vehicleMetricAPIParam.vehicleAdvertSnapshotId });
      //         }
      //      }
      //      else
      //      {
      //         var responseContent = await response.Content.ReadAsStringAsync();

      //         string error = $"GetAdvertsWithRetailRatingAtMultipleLocationsAPICall | Request: URL={request.RequestUri}, Body={jsonPayload} | Response: StatusCode={response.StatusCode}, Headers={string.Join(", ", response.Headers.Select(h => $"{h.Key}={string.Join(",", h.Value)}"))}, Body={responseContent}";
      //         var errorMessage = $"Non success code on VehicleMetricAPIResponse {RetailerIdSwapService.ProvideUpdatedId(vehicleMetricAPIParam.advertiserId)}  | {vehicleMetricAPIParam.vehicle.derivativeId}.  Error is {error}";
      //         vehicleMetricAPIResponses.Add(new VehicleMetricAPIResponseBasedOnLongLat() { errorMessage = errorMessage, vehicleAdvertSnapshotId = vehicleMetricAPIParam.vehicleAdvertSnapshotId });
      //      }
      //   }
      //}

      public async Task<VehicleMetricProcessedLocation> GetRetailRatingAndDTSAtSpecificLongLat(decimal longitude, decimal latitude, string derivativeId, int odometer, DateTime firstRegDate, int advertiserId, TokenResponse _bearerToken, string autotraderBaseURL)
      {
         string url = $"{autotraderBaseURL}/vehicle-metrics?advertiserId={RetailerIdSwapService.ProvideUpdatedId(advertiserId)}";
         var request = new HttpRequestMessage(HttpMethod.Post, url);
         _bearerToken = await autoTraderApiTokenClient.CheckExpiryAndRegenerate(_bearerToken);
         request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", _bearerToken.AccessToken);

         VehicleMetricParams paramsSet = new VehicleMetricParams()
         {
            vehicleAdvertSnapshotId = 0,
            advertiserId = advertiserId.ToString(),
            derivativeId = derivativeId,
            firstRegistrationDate = firstRegDate.ToString("yyyy-MM-dd"),
            odometerReadingMiles = odometer,
            locations = new List<VehicleMetricLocationParams>()
            {
               new VehicleMetricLocationParams()
               {
                  longitude = longitude,
                  latitude = latitude,
                  advertiserId = advertiserId.ToString()
               }
            }
         };
         VehicleMetricAPIParamBasedOnLongLat parmsToUse = new VehicleMetricAPIParamBasedOnLongLat(paramsSet);
         string jsonPayload = System.Text.Json.JsonSerializer.Serialize(parmsToUse);
         request.Content = new StringContent(jsonPayload, Encoding.UTF8, "application/json");

         var response = await SendRateLimitedRequestAsync(request);

         if (response.IsSuccessStatusCode)
         {
            var responseContent = await response.Content.ReadAsStringAsync();
            var jsonResult = responseContent;
            try
            {
               VehicleMetricAPIResponseError vehicleMetricAPIResponseError = System.Text.Json.JsonSerializer.Deserialize<VehicleMetricAPIResponseError>(jsonResult);

               if (vehicleMetricAPIResponseError?.messages?.FirstOrDefault()?.type == "ERROR")
               {
                  //errored
                  return null;
                  //vehicleMetricResponses.Add(new VehicleMetricProcessedResponse() { errorMessage = vehicleMetricAPIResponseError.messages.First().message, vehicleAdvertSnapshotId = paramsSet.vehicleAdvertSnapshotId });
               }
               else
               {
                  //success!
                  VehicleMetricAPIResponse vehicleMetricAPIResponse = System.Text.Json.JsonSerializer.Deserialize<VehicleMetricAPIResponse>(jsonResult);
                  vehicleMetricAPIResponse.errorMessage = string.Empty;
                  vehicleMetricAPIResponse.vehicleAdvertSnapshotId = paramsSet.vehicleAdvertSnapshotId;

                  //now convert the returned response into a processed response
                  var processed = new VehicleMetricProcessedResponse(vehicleMetricAPIResponse);
                  return processed.locations.FirstOrDefault();
               }
            }
            catch (Exception ex)
            {
               //failed
               return null;
            }
         }
         else
         {
            //failed
            var responseContent = await response.Content.ReadAsStringAsync();

            string error = $"GetAdvertsWithRetailRatingAtMultipleLocationsAPICall | Request: URL={request.RequestUri}, Body={jsonPayload} | Response: StatusCode={response.StatusCode}, Headers={string.Join(", ", response.Headers.Select(h => $"{h.Key}={string.Join(",", h.Value)}"))}, Body={responseContent}";
            var errorMessage = $"Non success code on VehicleMetricAPIResponse {RetailerIdSwapService.ProvideUpdatedId(paramsSet.advertiserId)}  | {paramsSet.derivativeId}.  Error is {error}";
            return null;

         }
      }



      private async Task GetAdvertsWithRetailRatingAtMultipleLocationsBatches(
         VehicleMetricParams vehicleMetricParam,
         string autotraderBaseURL,
         TokenResponse _bearerToken,
         List<VehicleMetricProcessedResponse> vehicleMetricResponses)
      {

         List<VehicleMetricProcessedResponse> vehicleMetricAPIResponses = new List<VehicleMetricProcessedResponse>();

         //Split the request
         int batchSize = 20;


         var locationChunks = vehicleMetricParam.locations
             .Select((item, index) => new { item, index })
             .GroupBy(x => x.index / batchSize)
             .Select(group => group.Select(x => x.item).ToList())
             .ToList();

         

         foreach (var locationChunk in locationChunks)
         {
            vehicleMetricParam.locations = locationChunk;
            await GetAdvertsWithRetailRatingAtMultipleLocationsAPICallNEW(vehicleMetricParam, autotraderBaseURL, _bearerToken, vehicleMetricAPIResponses);
         }

         // Create merged response
         var mergedLocationVehicleMetricResponse = vehicleMetricAPIResponses.First();
         mergedLocationVehicleMetricResponse.locations = vehicleMetricAPIResponses.ToList().Where(r => r?.locations != null).SelectMany(r => r.locations).ToList();
         
         vehicleMetricResponses.Add(mergedLocationVehicleMetricResponse);
      }




      private async Task GetAdvertsWithRetailRatingAtMultipleLocationsAPICallNEW(
   VehicleMetricParams vehicleMetricParam,
   string autotraderBaseURL,
   TokenResponse _bearerToken,
   List<VehicleMetricProcessedResponse> vehicleMetricResponses)
      {
            string url = $"{autotraderBaseURL}/vehicle-metrics?advertiserId={RetailerIdSwapService.ProvideUpdatedId(vehicleMetricParam.advertiserId)}";
            var request = new HttpRequestMessage(HttpMethod.Post, url);
            _bearerToken = await autoTraderApiTokenClient.CheckExpiryAndRegenerate(_bearerToken);
            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", _bearerToken.AccessToken);


            bool haveAdvertiserIds = vehicleMetricParam.locations.All(x => x.advertiserId != null);
            if (haveAdvertiserIds)
            {
               //have to convert the params into a suitable form for the autotrader API to receive
               VehicleMetricAPIParam parmsToUse = new VehicleMetricAPIParam(vehicleMetricParam);
               string jsonPayload = System.Text.Json.JsonSerializer.Serialize(parmsToUse);
               request.Content = new StringContent(jsonPayload, Encoding.UTF8, "application/json");

               var response = await SendRateLimitedRequestAsync(request);

               if (response.IsSuccessStatusCode)
               {
                  var responseContent = await response.Content.ReadAsStringAsync();
                  var jsonResult = responseContent;
                  try
                  {
                     VehicleMetricAPIResponseError vehicleMetricAPIResponseError = System.Text.Json.JsonSerializer.Deserialize<VehicleMetricAPIResponseError>(jsonResult);

                     if (vehicleMetricAPIResponseError?.messages?.FirstOrDefault()?.type == "ERROR")
                     {
                        //errored
                        vehicleMetricResponses.Add(new VehicleMetricProcessedResponse() { errorMessage = vehicleMetricAPIResponseError.messages.First().message, vehicleAdvertSnapshotId = vehicleMetricParam.vehicleAdvertSnapshotId });
                     }
                     else
                     {
                        //success!
                        VehicleMetricAPIResponse vehicleMetricAPIResponse = System.Text.Json.JsonSerializer.Deserialize<VehicleMetricAPIResponse>(jsonResult);
                        vehicleMetricAPIResponse.errorMessage = string.Empty;
                        vehicleMetricAPIResponse.vehicleAdvertSnapshotId = vehicleMetricParam.vehicleAdvertSnapshotId;

                        //now convert the returned response into a processed response
                        var processed = new VehicleMetricProcessedResponse(vehicleMetricAPIResponse);
                        //now transfer the retailerSiteIds into the locations
                        Dictionary<string, VehicleMetricLocationParams> retailerLookup = vehicleMetricParam.locations.ToDictionary(x => x.advertiserId);
                        if (processed.locations != null)
                        {
                           foreach (var location in processed.locations)
                           {
                              var locationParams = retailerLookup[location.advertiserId];
                              location.RetailerSiteId = locationParams.RetailerSiteId;
                           }
                        }
                        vehicleMetricResponses.Add(processed);
                     }
                  }
                  catch (Exception ex)
                  {
                     //failed
                     vehicleMetricResponses.Add(new VehicleMetricProcessedResponse() { errorMessage = ex.Message, vehicleAdvertSnapshotId = vehicleMetricParam.vehicleAdvertSnapshotId });
                  }
               }
               else
               {
                  //failed
                  var responseContent = await response.Content.ReadAsStringAsync();

                  string error = $"GetAdvertsWithRetailRatingAtMultipleLocationsAPICall | Request: URL={request.RequestUri}, Body={jsonPayload} | Response: StatusCode={response.StatusCode}, Headers={string.Join(", ", response.Headers.Select(h => $"{h.Key}={string.Join(",", h.Value)}"))}, Body={responseContent}";
                  var errorMessage = $"Non success code on VehicleMetricAPIResponse {RetailerIdSwapService.ProvideUpdatedId(vehicleMetricParam.advertiserId)}  | {vehicleMetricParam.derivativeId}.  Error is {error}";
                  vehicleMetricResponses.Add(new VehicleMetricProcessedResponse() { errorMessage = errorMessage, vehicleAdvertSnapshotId = vehicleMetricParam.vehicleAdvertSnapshotId });
               }


            }
            else
            {
               //we don't have advertiserIds so we use the long lat approach
               bool haveLongLats = vehicleMetricParam.locations.All(x => x.longitude != null && x.latitude != null);
               if (!haveLongLats)
               {
                  throw new Exception("GetAdvertsWithRetailRatingAtMultipleLocationsAPICallNEW requires all locations to have an advertiserId or a longLat");
               }
               //we have longlats, proceed
               VehicleMetricAPIParamBasedOnLongLat parmsToUse = new VehicleMetricAPIParamBasedOnLongLat(vehicleMetricParam);
               string jsonPayload = System.Text.Json.JsonSerializer.Serialize(parmsToUse);
               request.Content = new StringContent(jsonPayload, Encoding.UTF8, "application/json");

               var response = await SendRateLimitedRequestAsync(request);

               if (response.IsSuccessStatusCode)
               {
                  var responseContent = await response.Content.ReadAsStringAsync();
                  var jsonResult = responseContent;
                  try
                  {
                     VehicleMetricAPIResponseError vehicleMetricAPIResponseError = System.Text.Json.JsonSerializer.Deserialize<VehicleMetricAPIResponseError>(jsonResult);

                     if (vehicleMetricAPIResponseError?.messages?.FirstOrDefault()?.type == "ERROR")
                     {
                        //errored
                        vehicleMetricResponses.Add(new VehicleMetricProcessedResponse() { errorMessage = vehicleMetricAPIResponseError.messages.First().message, vehicleAdvertSnapshotId = vehicleMetricParam.vehicleAdvertSnapshotId });
                     }
                     else
                     {
                        //success!
                        VehicleMetricAPIResponseBasedOnLongLat vehicleMetricAPIResponse = System.Text.Json.JsonSerializer.Deserialize<VehicleMetricAPIResponseBasedOnLongLat>(jsonResult);
                        vehicleMetricAPIResponse.errorMessage = string.Empty;
                        vehicleMetricAPIResponse.vehicleAdvertSnapshotId = vehicleMetricParam.vehicleAdvertSnapshotId;

                        //now convert the returned response into a processed response
                        var processed = new VehicleMetricProcessedResponse(vehicleMetricAPIResponse);
                        //now transfer the retailerSiteIds into the locations
                        Dictionary<Tuple<decimal?, decimal?>, VehicleMetricLocationParams> retailerLookup = vehicleMetricParam.locations.ToDictionary(x => Tuple.Create(x.longitude, x.latitude), x => x);
                        foreach (var location in processed.locations)
                        {
                           var locationParams = retailerLookup[Tuple.Create(location.longitude, location.latitude)];
                           location.RetailerSiteId = locationParams.RetailerSiteId;
                        }
                        vehicleMetricResponses.Add(processed);
                     }
                  }
                  catch (Exception ex)
                  {
                     //failed
                     vehicleMetricResponses.Add(new VehicleMetricProcessedResponse() { errorMessage = ex.Message, vehicleAdvertSnapshotId = vehicleMetricParam.vehicleAdvertSnapshotId });
                  }
               }
               else
               {
                  //failed
                  var responseContent = await response.Content.ReadAsStringAsync();

                  string error = $"GetAdvertsWithRetailRatingAtMultipleLocationsAPICall | Request: URL={request.RequestUri}, Body={jsonPayload} | Response: StatusCode={response.StatusCode}, Headers={string.Join(", ", response.Headers.Select(h => $"{h.Key}={string.Join(",", h.Value)}"))}, Body={responseContent}";
                  var errorMessage = $"Non success code on VehicleMetricAPIResponse {RetailerIdSwapService.ProvideUpdatedId(vehicleMetricParam.advertiserId)}  | {vehicleMetricParam.derivativeId}.  Error is {error}";
                  vehicleMetricResponses.Add(new VehicleMetricProcessedResponse() { errorMessage = errorMessage, vehicleAdvertSnapshotId = vehicleMetricParam.vehicleAdvertSnapshotId });
               }



            }

      }



      #endregion




      private async Task<HttpResponseMessage> SendRateLimitedRequestAsync(HttpRequestMessage request, CancellationToken cancellationToken = default) // Add CancellationToken if needed
      {
         using (RateLimitLease lease = await rateLimiter.AcquireAsync(1, cancellationToken))
         {
            if (!lease.IsAcquired)
            {
               // Handle rejection (e.g., throw RateLimiterRejectedException)
               throw new TimeoutException($"Rate limit permit could not be acquired for request to {request.RequestUri}.");
            }
            // Only send if lease was acquired
            return await _httpClient.SendAsync(request, cancellationToken);
         }
      }















   }
}
