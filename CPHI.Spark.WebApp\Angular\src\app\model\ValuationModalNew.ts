import { CompetitorSummary } from "./CompetitorSummary";
import { LeavingVehicleItem } from "./LeavingVehicleItem";
import { PrepCostItem } from "./PrepCostItem";
import { SameModelAdvert } from "./SameModelAdvert";
import { TrendedValuationCollection } from "./TrendedValuationCollection";
import { VehicleValuation } from "./VehicleValuation";
import { ValuationPriceSet } from "./ValuationPriceSet.model";
import { VehicleInformation } from "./VehicleInformation";
import { VehicleLocationStrategyPriceBuild } from "./VehicleLocationStrategyPriceBuild";
import { StockLevelAndCover } from "./StockLevelAndCover";
import { SameModelAdvertSummary } from "./SameModelAdvertSummary";
import { VehicleSpecOption } from "./VehicleSpecOption.model";
import { VehicleValuationWithStrategyPrice } from "./VehicleValuationWithStrategyPrice";
import { TradePriceSetting } from "./TradePriceSetting";


export interface ValuationModalNew {
    VehicleInformation: VehicleInformation;
    TrendedValuations: TrendedValuationCollection;
    ValuationPriceSet: ValuationPriceSet;
    LocationStrategyPrices: VehicleLocationStrategyPriceBuild[];
    CompetitorCheckResult: CompetitorSummary;
    RecentlySoldThisModel: LeavingVehicleItem[];
    SameModelSummary: SameModelAdvertSummary;
    PrepCostItems: PrepCostItem[];
    AvgPrepCost: number;
    Valuation: VehicleValuationWithStrategyPrice;
    StockLevelAndCover: StockLevelAndCover[];
    SpecOptions: VehicleSpecOption[];
    TradePriceSetting: TradePriceSetting;
    ErrorMessage: string;
}


