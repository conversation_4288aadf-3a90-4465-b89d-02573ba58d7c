﻿using CPHI.Spark.Model;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using Newtonsoft.Json;
using System.Net.Http.Headers;
using System.Text;
using System.Collections.Concurrent;
using log4net;
using CPHI.Spark.Model.AutoPrice;
using Microsoft.IdentityModel.Tokens;
using System.Threading.RateLimiting;
using CPHI.Spark.DataAccess.DataAccess.AutoPrice;



namespace CPHI.Spark.DataAccess.AutoPrice
{

   public interface IAutoTraderFutureValuationsClient
   {
      Task<List<FutureValuationPoint>> GetFutureValuation(GetFutureValuationsParams parms, TokenResponse tokenResponse,ILog? logger);
      Task<List<FutureValuationPoint>> GetFutureValuationForNewVehicle(GetFutureValuationsNewParams parms, ILog? logger);
   }



   public class AutoTraderFutureValuationsClient : IAutoTraderFutureValuationsClient
   {

      private readonly HttpClient _httpClient;
      private string atApiKey;
      private string atApiSecret;
      private string atBaseURL;
      private readonly IAutoTraderApiTokenClient autoTraderApiTokenClient;
      private readonly RateLimiter rateLimiter;



      public AutoTraderFutureValuationsClient(IHttpClientFactory httpClientFactory, string atApiKeyIn, string atApiSecretIn, string atBaseURLIn) //, FixedWindowRateLimiter rateLimiterIn
      {
         _httpClient = httpClientFactory.CreateClient();

         atApiKey = atApiKeyIn;
         atApiSecret = atApiSecretIn;
         atBaseURL = atBaseURLIn;

         autoTraderApiTokenClient = new AutoTraderApiTokenClient(httpClientFactory, atApiKey, atApiSecret, atBaseURL);

         rateLimiter = AutotraderRateLimiter.FutureValuationsLimiter;
      }

      //------------------------------------------------------------------
      #region Future Valuations
      //------------------------------------------------------------------

      public async Task<List<FutureValuationPoint>> GetFutureValuation(GetFutureValuationsParams parms, TokenResponse tokenResponse, ILog? logger)
      {

         //TokenResponse tokenResponse = await GetToken();

         ConcurrentBag<FutureValuationPoint> futureValuationResults = new ConcurrentBag<FutureValuationPoint>();

         string url = $"https://api.autotrader.co.uk/future-valuations?advertiserId={RetailerIdSwapService.ProvideUpdatedId(parms.retailerId)}";
         List<FutureValuationParam> futureValuationParams = new List<FutureValuationParam>();

         //build params for api call
         foreach (var futureValuationPoint in parms.futureValuationPoints)
         {
            futureValuationParams.Add(new FutureValuationParam()
            {
               futureValuationDate = futureValuationPoint.Date,
               vehicle = new VehicleParam()
               {
                  derivativeId = parms.derivativeId,
                  firstRegistrationDate = parms.firstRegistrationDate,
                  futureOdometerReadingMiles = parms.odometerReading
               }

            });
         }

         List<Task> tasks = new List<Task>();
         foreach (var futureValuationParam in futureValuationParams)
         {
            tasks.Add(ExecuteGetFutureValuation(url, futureValuationParam, tokenResponse, futureValuationResults, parms.currentValuation, logger));
         }

         await Task.WhenAll(tasks);

         //now must adjust valuations based upon this vehicle actual valuation vs average vehicle
         decimal adjustedVsAverageValuation = parms.currentValuation > 0 ? parms.currentAdjustedValuation / parms.currentValuation : 0;
         foreach (var valuation in futureValuationResults)
         {
            valuation.RetailValue = (int)Math.Round(valuation.RetailValue * adjustedVsAverageValuation, 0);
         }
         return futureValuationResults.OrderBy(f => f.TimePoint).ToList();
      }


      public async Task<List<FutureValuationPoint>> GetFutureValuationForNewVehicle(GetFutureValuationsNewParams parms, ILog? logger)
      {

         TokenResponse tokenResponse = await autoTraderApiTokenClient.GetToken();

         ConcurrentBag<FutureValuationPoint> futureValuationResults = new ConcurrentBag<FutureValuationPoint>();

         string url = $"https://api.autotrader.co.uk/future-valuations?advertiserId={RetailerIdSwapService.ProvideUpdatedId(parms.retailerId)}";
         List<FutureValuationParam> futureValuationParams = new List<FutureValuationParam>();

         //build params for api call
         foreach (var futureValuationPoint in parms.futureValuationPoints)
         {
            futureValuationParams.Add(new FutureValuationParam()
            {
               futureValuationDate = futureValuationPoint.date,
               vehicle = new VehicleParam()
               {
                  derivativeId = parms.derivativeId,
                  firstRegistrationDate = DateTime.Now,
                  futureOdometerReadingMiles = futureValuationPoint.odometerReading
               }

            });
         }

         List<Task> tasks = new List<Task>();
         foreach (var futureValuationParam in futureValuationParams)
         {
            tasks.Add(ExecuteGetFutureValuation(url, futureValuationParam, tokenResponse, futureValuationResults, parms.currentValuation,logger));
         }

         await Task.WhenAll(tasks);

         return futureValuationResults.OrderBy(f => f.TimePoint).ToList();
      }

      private async Task ExecuteGetFutureValuation(string url,
      FutureValuationParam futureValuationParam,
      TokenResponse tokenResponse,
      ConcurrentBag<FutureValuationPoint> futureValuationResults,
      decimal currentValuation,
      ILog? logger
      )
      {
         var request = new HttpRequestMessage(HttpMethod.Post, url);
         //if(futureValuationParam.vehicle.futureOdometerReadingMiles < 500)
         //{
         //   futureValuationParam.vehicle.futureOdometerReadingMiles = 500;
         //}

         string jsonPayload = System.Text.Json.JsonSerializer.Serialize(futureValuationParam);

         request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", tokenResponse.AccessToken);
         request.Content = new StringContent(jsonPayload, Encoding.UTF8, "application/json");

         var response = await SendRateLimitedRequestAsync(request);

         if (response.IsSuccessStatusCode)
         {
            var responseContent = await response.Content.ReadAsStringAsync();
            var jsonResult = responseContent;
            try
            {
               FutureValuationRoot futureValuationRoot = System.Text.Json.JsonSerializer.Deserialize<FutureValuationRoot>(jsonResult);
               futureValuationRoot.errorMessage = string.Empty;
               futureValuationResults.Add(new FutureValuationPoint(futureValuationRoot.futureValuations, futureValuationParam.futureValuationDate, (int)currentValuation));
            }
            catch (Exception ex)
            {
               { }
               //throw new Exception(ex.Message, ex);

               var errorMessage = $"Non success code on futureValuationPoints {futureValuationParam.futureValuationDate} {ex.Message} | {responseContent}";
               if (logger != null)
               {
                  logger.Error(errorMessage);
               }
               futureValuationResults.Add(new FutureValuationPoint());
            }
         }
         else
         {
            //throw new Exception($"Unable to retrieve data: {response.StatusCode}");
            var responseContent = await response.Content.ReadAsStringAsync();
            var errorMessage = $"Non success code on futureValuationPoints {futureValuationParam.futureValuationDate} {response.StatusCode} | {responseContent}";
            if (logger != null)
            {
               logger.Error(errorMessage);
            }
            futureValuationResults.Add(new FutureValuationPoint());
         }
      }

      #endregion


      private async Task<HttpResponseMessage> SendRateLimitedRequestAsync(HttpRequestMessage request, CancellationToken cancellationToken = default) // Add CancellationToken if needed
      {
         using (RateLimitLease lease = await rateLimiter.AcquireAsync(1, cancellationToken))
         {
            if (!lease.IsAcquired)
            {
               // Handle rejection (e.g., throw RateLimiterRejectedException)
               throw new TimeoutException($"Rate limit permit could not be acquired for request to {request.RequestUri}.");
            }
            // Only send if lease was acquired
            return await _httpClient.SendAsync(request, cancellationToken);
         }
      }















   }
}
