import { FleetOrderComment } from "./FleetOrderComment";
import { FleetOrderbookComment } from "./FleetOrderbookComment";



export class FleetOrderbookRow {

    constructor(fleetOrderbookRowIn: FleetOrderbookRow) {
        this.RenaultOrNissanOrderItemId = fleetOrderbookRowIn.RenaultOrNissanOrderItemId;
        this.Chassis = fleetOrderbookRowIn.Chassis;
        this.VehicleOrderNumber = fleetOrderbookRowIn.VehicleOrderNumber;
        this.OwningMainDealer = fleetOrderbookRowIn.OwningMainDealer;
        this.Customer = fleetOrderbookRowIn.Customer;
        this.Status = fleetOrderbookRowIn.Status;
        this.Model = fleetOrderbookRowIn.Model;
        this.Version = fleetOrderbookRowIn.Version;
        this.SalesExecPin = fleetOrderbookRowIn.SalesExecPin;
        this.Colour = fleetOrderbookRowIn.Colour;
        this.Options = fleetOrderbookRowIn.Options;
        this.LabelOptions = fleetOrderbookRowIn.LabelOptions;
        this.SalesChannel = fleetOrderbookRowIn.SalesChannel;
        this.Comments = fleetOrderbookRowIn.Comments;
        this.EbbonStatus = fleetOrderbookRowIn.EbbonStatus;
        this.OrderDate = fleetOrderbookRowIn.OrderDate;
        this.ActualMatchedDate = fleetOrderbookRowIn.ActualMatchedDate;
        this.FRD = fleetOrderbookRowIn.FRD;
        this.LeaseNumber = fleetOrderbookRowIn.LeaseNumber;
        this.PromAtic = fleetOrderbookRowIn.PromAtic;
        this.ProbableAtic = fleetOrderbookRowIn.ProbableAtic;
        this.ActualAtic = fleetOrderbookRowIn.ActualAtic;
        this.DDDDate = fleetOrderbookRowIn.DDDDate;
        this.RegistrationDate = fleetOrderbookRowIn.RegistrationDate;
        this.CustomerType = fleetOrderbookRowIn.CustomerType;
        this.TrueAge = fleetOrderbookRowIn.TrueAge;
        this.AgeAtEom = fleetOrderbookRowIn.AgeAtEom;
        this.DeliveryDate = fleetOrderbookRowIn.DeliveryDate;
        this.DeliveryAccount = fleetOrderbookRowIn.DeliveryAccount;
        this.Brand = fleetOrderbookRowIn.Brand;
        this.FonName = fleetOrderbookRowIn.FonName;
        this.FonData = fleetOrderbookRowIn.FonData;
        this.AlarmData = fleetOrderbookRowIn.AlarmData;
        this.StockCategory = fleetOrderbookRowIn.StockCategory;
        this.BcaStatus = fleetOrderbookRowIn.BcaStatus;
        this.BcaArrivalDate = fleetOrderbookRowIn.BcaArrivalDate;
        this.BcaDdd = fleetOrderbookRowIn.BcaDdd;
        this.BcaEstArrivalDate = fleetOrderbookRowIn.BcaEstArrivalDate;
        this.SparkComments = fleetOrderbookRowIn.SparkComments;
        this.StockNumber = fleetOrderbookRowIn.StockNumber;
        this.TotalNLProfit = fleetOrderbookRowIn.TotalNLProfit;
        this.InvoiceDateToCustomer = fleetOrderbookRowIn.InvoiceDateToCustomer;
        this.IsInvoicedInMonth = fleetOrderbookRowIn.IsInvoicedInMonth;
        this.IsRegisteredInMonth = fleetOrderbookRowIn.IsRegisteredInMonth;
        this.IsMatchedInMonth = fleetOrderbookRowIn.IsMatchedInMonth;
        this.GFCStockAge = fleetOrderbookRowIn.GFCStockAge;
        this.Reg = fleetOrderbookRowIn.Reg;
        this.ReportReg = fleetOrderbookRowIn.ReportReg;
        this.DealsReg = fleetOrderbookRowIn.DealsReg;
        this.DeliveryMonth = fleetOrderbookRowIn.DeliveryMonth;
        this.IsHidden = fleetOrderbookRowIn.IsHidden;
        this.IsRemoved = fleetOrderbookRowIn.IsRemoved;
        this.RemovedDate = fleetOrderbookRowIn.RemovedDate;
        this.Comment = fleetOrderbookRowIn.Comment;
        this.OrderComments = fleetOrderbookRowIn.OrderComments;
        this.DropCode = fleetOrderbookRowIn.DropCode;
        this.EndUser = fleetOrderbookRowIn.EndUser;
        this.FonCode = fleetOrderbookRowIn.FonCode;
        this.FonDescription = fleetOrderbookRowIn.FonDescription;
        this.CustomerOrderNo = fleetOrderbookRowIn.CustomerOrderNo;
        this.DeliveryDepot = fleetOrderbookRowIn.DeliveryDepot;
        this.DriverPackRequired = fleetOrderbookRowIn.DriverPackRequired;
        this.DriverPackOrdered = fleetOrderbookRowIn.DriverPackOrdered;
        this.PivgCreationDate = fleetOrderbookRowIn.PivgCreationDate;
        this.PivgExpiryDate = fleetOrderbookRowIn.PivgExpiryDate;
        this.InventoryType = fleetOrderbookRowIn.InventoryType;
        this.SalesPerson = fleetOrderbookRowIn.SalesPerson;
        this.CarPersonalisationAgeEoM = fleetOrderbookRowIn.CarPersonalisationAgeEoM;
        this.FullConversionAgeEoM = fleetOrderbookRowIn.FullConversionAgeEoM;
        this.AgeingProfile = fleetOrderbookRowIn.AgeingProfile;
        this.PlyOrderDate = fleetOrderbookRowIn.PlyOrderDate;
        this.PlyFitmentDate = fleetOrderbookRowIn.PlyFitmentDate;
        this.PlyComplDate = fleetOrderbookRowIn.PlyComplDate;
        this.PlyAccName = fleetOrderbookRowIn.PlyAccName;
        this.PlyDealerCost = fleetOrderbookRowIn.PlyDealerCost;
        this.Campaign = fleetOrderbookRowIn.Campaign;
        this.Mechanical = fleetOrderbookRowIn.Mechanical;
        this.Estimate = fleetOrderbookRowIn.Estimate;
        this.Damage = fleetOrderbookRowIn.Damage;
        this.IsOnLatestDownload = fleetOrderbookRowIn.IsOnLatestDownload;
        this.delDueNotRegd = fleetOrderbookRowIn.delDueNotRegd;
        this.regMismatch = fleetOrderbookRowIn.regMismatch;
        this.VehicleOrderNumberNoLeadingLetters = fleetOrderbookRowIn.VehicleOrderNumberNoLeadingLetters;
        this.DueATIC = fleetOrderbookRowIn.DueATIC;
        this.RegMismatch = fleetOrderbookRowIn.RegMismatch;
        this.RegDate = fleetOrderbookRowIn.RegDate;
        this.EbbonStage = fleetOrderbookRowIn.EbbonStage;
        this.Count = fleetOrderbookRowIn.Count;
        this.ConverterName = fleetOrderbookRowIn.ConverterName;
        this.AccountHandlerName = fleetOrderbookRowIn.AccountHandlerName;
        this.PivgReference = fleetOrderbookRowIn.PivgReference;
        this.DeliveryAgent = fleetOrderbookRowIn.DeliveryAgent;
        this.ConvBldDte = fleetOrderbookRowIn.ConvBldDte;
        this.ConvEstComplDte = fleetOrderbookRowIn.ConvEstComplDte;
        this.RUKIsGoing = fleetOrderbookRowIn.RUKIsGoing;
        this.RUKForecastReason = fleetOrderbookRowIn.RUKForecastReason;
        this.RUKForecastSubReason = fleetOrderbookRowIn.RUKForecastSubReason;
        this.RUKForecastFRD = fleetOrderbookRowIn.RUKForecastFRD;

        this.comments = fleetOrderbookRowIn.comments;

        this.updateForecastReasons();
    }

    RenaultOrNissanOrderItemId: number;
    Chassis: string;
    VehicleOrderNumber: string;
    OwningMainDealer: number;
    Customer: string;
    Status: string;
    Model: string;
    Version: string;
    SalesExecPin: number;
    Colour: string;
    Options: string;
    LabelOptions: string;
    SalesChannel: string;
    Comments: string;
    LeaseNumber: string;
    EbbonStatus: string;
    OrderDate: Date | string | null;
    ActualMatchedDate: Date | string | null;
    FRD: Date | string | null;
    PromAtic: Date | string | null;
    ProbableAtic: Date | string | null;
    ActualAtic: Date | string | null;
    DDDDate: Date | string | null;
    RegistrationDate: Date | string | null;
    CustomerType: string;
    TrueAge: number;
    AgeAtEom: number;
    DeliveryDate: Date | string | null;
    DeliveryAccount: string;
    DeliveryAgent: string;
    Brand: string; // Nissan / Renault

    FonName: string;
    FonData: string;

    AlarmData: string;
    StockCategory: string;

    BcaStatus: string;
    BcaArrivalDate: Date | string | null;
    BcaDdd: Date | string | null;
    BcaEstArrivalDate: Date | string | null;

    SparkComments: string;

    StockNumber: string;
    TotalNLProfit: number;
    InvoiceDateToCustomer: Date | string | null;
    IsInvoicedInMonth: string;
    IsRegisteredInMonth: string;
    IsMatchedInMonth: string;
    GFCStockAge: number;
    Reg: string;
    ReportReg: string;
    DealsReg: string;
    DeliveryMonth: string;

    IsHidden: boolean;
    IsRemoved: boolean;
    RemovedDate: Date | string;

    //extras
    Comment: string;
    OrderComments: string;
    DropCode: string;
    EndUser: string;
    FonCode: string;
    FonDescription: string;
    CustomerOrderNo: string;
    DeliveryDepot: string;
    DriverPackRequired: boolean;
    DriverPackOrdered: boolean;
    PivgReference: string;
    PivgCreationDate: Date | string | null;
    PivgExpiryDate: Date | string | null;
    InventoryType: string;
    SalesPerson: string;
    CarPersonalisationAgeEoM: string;
    FullConversionAgeEoM: string;

    //Ren extras
    AgeingProfile: string;

    // For Ply Report
    PlyOrderDate: Date;
    PlyFitmentDate: Date;
    PlyComplDate: string;
    PlyAccName: string;
    PlyDealerCost: number;

    //BCA extra
    Campaign: number;
    Mechanical: number;
    Estimate: number;
    Damage: number;
    IsOnLatestDownload: boolean;

    delDueNotRegd?: boolean;
    regMismatch?: boolean;
    VehicleOrderNumberNoLeadingLetters: string;
    DueATIC: Date | string | null;
    RegMismatch: boolean;
    RegDate: Date | string | null;
    EbbonStage: string;
    Count: number;

    ConverterName: string;
    AccountHandlerName: string;
    ConvBldDte: Date;
    ConvEstComplDte: Date;



    //spk-4313 4 more fields
    RUKIsGoing: string;
    RUKForecastReason: string;
    RUKForecastSubReason: string;
    RUKForecastFRD: Date | string | null;


    //local props
    comments: FleetOrderbookComment[]
    missingReason: boolean;
    missingSubReason: boolean;
    missingFRD: boolean;



    updateForecastReasons() {

        const reasonsThatRequireSubReason: string[] = [
            'Sold add. Works / conversion required',
            'Sold conversion required',
            `Sold, customer can't take delivery`,
            'Unsold, add. Works / conversion required',
            'Unsold, conversion required',
        ]

        if (this.RUKIsGoing == 'Not Going') {
            //arrange for FRD
            this.missingFRD = !this.RUKForecastFRD
            this.missingReason = !this.RUKForecastReason

            if (this.RUKForecastReason) {
                if (reasonsThatRequireSubReason.includes(this.RUKForecastReason)) {
                    this.missingSubReason = !this.RUKForecastSubReason;
                }
            }
        } else {
            //Is going
            this.missingReason = false;
            this.missingSubReason = false;
            this.missingFRD = false;
        }
    }

}

