﻿using CPHI.Spark.Model.ViewModels.AutoPricing;
using System.Collections.Generic;
using System.Linq;

namespace CPHI.Spark.Model.ViewModels
{
   public class ValuationModal
   {
      public VehicleValuationInformation VehicleValuationInformation { get; set; }
      public ValuationPriceSet Valuation { get; set; }
      public List<PrepCostItem> PrepCostItems { get; set; }
      public IEnumerable<StockLevelAndCover> StockLevelAndCover { get; set; }
      public CompetitorSummary CompetitorSummary { get; set; }
      public List<LocationAndStrategyPrice> LocationAndStrategyPrices { get; set; }
      public List<VehicleSpecOption> VehicleSpecOptions { get; set; }
      public decimal TargetProfit { get; set; }
      public SellingOutlook SellingOutlook { get; set; }
      public ValuationCostingDTO Costing { get; set; }
   }


   public class ValuationModalNew
   {
      public VehicleInformation VehicleInformation { get; set; }
      public TrendedValuationCollection TrendedValuations { get; set; }
      public ValuationPriceSet ValuationPriceSet { get; set; }
      public List<VehicleLocationStrategyPriceBuild> LocationStrategyPrices { get; set; }
      public CompetitorSummary CompetitorCheckResult { get; set; }
      public List<LeavingVehicleItem> RecentlySoldThisModel { get; set; }
      public SameModelAdvertSummary SameModelSummary { get; set; }
      public List<PrepCostItem> PrepCostItems { get; set; }
      public decimal AvgPrepCost
      {
         get
         {
            if (PrepCostItems == null || PrepCostItems.Count == 0)
            {
               return 0;
            }

            return PrepCostItems.Sum(item => item.PrepCost) / PrepCostItems.Count;
         }
      }
      public VehicleValuationWithStrategyPrice Valuation { get; set; }
      public List<StockLevelAndCover> StockLevelAndCover { get; set; }
      public List<VehicleSpecOption> SpecOptions { get; set; }
      public TradePriceSetting TradePriceSetting { get; set; }
      public string ErrorMessage { get; set; } = null;

   }

}
