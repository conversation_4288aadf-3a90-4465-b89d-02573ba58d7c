﻿using CPHI.Spark.BusinessLogic.AutoPrice;
using CPHI.Spark.DataAccess;
using CPHI.Spark.DataAccess.AutoPrice;
using CPHI.Spark.Model;
using CPHI.Spark.Model.autoPricing;
using CPHI.Spark.Model.Services;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using CPHI.Spark.Model.ViewModels.AutoPricing.RawDataTypes;
using CPHI.Spark.Repository;
using Datadog.Trace;
using log4net;
using MoreLinq.Extensions;
using OfficeOpenXml.FormulaParsing.Excel.Functions.DateTime;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Diagnostics;
using System.Drawing.Printing;
using System.Linq;
using System.Net.Http;
using System.Reflection;
using System.Security.Policy;
using System.Text;
using System.Threading;
using System.Threading.RateLimiting;
using System.Threading.Tasks;
using MoreLinq;
using static CPHI.Spark.Model.ViewModels.AutoPricing.RetailCustomerLatestViewResponseResult;
using static log4net.Appender.ColoredConsoleAppender;
using static System.Net.WebRequestMethods;
using System.Runtime.CompilerServices;
using System.ComponentModel.DataAnnotations;
using CPHI.Spark.WebApp.DataAccess;

namespace CPHI.Spark.Reporter.Services.AutoPrice
{



   public class GetMarketWideAdvertsService
   {
      //private int AdsCount;
      List<AutotraderAdvertIncoming> allAdsToSave;
      List<AutotraderAdvertIncoming> allAds;
      //private List<string> allWebsiteStockIdentifiersSeen = new List<string>();
      private readonly HttpClient httpClient;
      //private string _baseURL = "https://api.autotrader.co.uk/stock";
      //private int _targetResultsMax = 180;
      //private int _targetResultsMin = 170;
      //private int _resultsHardLimit = 200;
      private string defaultPostcode = "LE15PX";
      private int defaultRadius = 1000;
      private readonly IHttpClientFactory _httpClientFactory;
      private AutoTraderCompetitorClient atCompetitorClient;
      private AutoTraderApiTokenClient atTokenClient;
      private TokenResponse _bearerToken;
      private ILog logger;

      public GetMarketWideAdvertsService(IHttpClientFactory httpClientFactory, ILog loggerIn)
      {
         this.httpClient = httpClientFactory.CreateClient();
         this._httpClientFactory = httpClientFactory;
         this.logger = loggerIn;
      }



      public async Task GetAds(List<Model.DealerGroupName> dealerGroups, bool isPrivate)
      {
         if (dealerGroups.Count == 0)
         { return; }
         try
         {
            logger.Info("----------------------------------------------------------");
            logger.Info("FetchMarketAds");

            foreach (Model.DealerGroupName dealerGroup in dealerGroups)
            {

               logger.Info($"FetchMarketAds: {dealerGroup}");
               var logMessage = LoggingService.InitLogMessage();
               try
               {
                  string connString = ConfigService.GetConnectionString(dealerGroup);
                  RetailerSitesDataAccess retailerSitesDataAccess = new RetailerSitesDataAccess(connString);
                  List<RetailerSite> retailers = await retailerSitesDataAccess.GetRetailerSites(dealerGroup);

                  if (retailers.Count > 0)
                  {
                     try
                     {
                        await ExecuteGetMarketAds(logger, isPrivate, dealerGroup);
                     }
                     catch (Exception ex)
                     {
                        logger.Error(ex);
                        await EmailerService.SendMailOnError(dealerGroup, "FetchMarketAds - Error", ex);
                        LoggingService.AddErrorLogMessage(logMessage, ex.Message);
                     }
                  }
                  else
                  {
                     logger.Info($"FetchMarketAds: {dealerGroup}: No retailers");
                  }
               }
               catch (Exception ex)
               {
                  await EmailerService.LogException(ex, logger, "FetchMarketAds");
                  LoggingService.AddErrorLogMessage(logMessage, ex.Message);
               }
               finally
               {
                  await LoggingService.FinalizeAndSaveLogMessage(dealerGroup, logMessage, "FetchMarketAds");
               }
            }

            logger.Info("Completed FetchMarketAds");
            logger.Info("----------------------------------------------------------");
         }
         catch (Exception e)
         {
            logger.Error(e);
            throw new Exception(e.Message, e);
         }
      }




      public async Task ExecuteGetMarketAds(ILog logger, bool isPrivate, DealerGroupName dealerGroup)
      {
         //we don't know the distribution.  so strategy is to ask for all ads sorted low to high on price, get first 10 pages.   find the price on ad 200.    walk backwards to find the highest price we
         //have this is below the final price.    we then know we have got all of those prices secured.
         //if we find that all 200 vehicles are the same price, we might have a problem so we have to now search by constraining on this price exactly i.e. 200 to 200, but sort by mileage low to high
         //we should now find we can do the same logic with the final odometer reading, once we are out of this loop, we know we have go this price point covered, so we can then continue upwards on our journey




         //--------------------------------------------------------------------------------
         // Setup
         //--------------------------------------------------------------------------------
         DateTime startDate = DateTime.Now;
         if (!isPrivate)
         {
            if (!await ShouldContinueRunningForTradeAds(logger, startDate, dealerGroup))
            {
               logger.Info("Skipping Trade Adverts as already run today.");
               return;
            }

         }
         RetailerSitesDataAccess retailerSitesDataAccess = new RetailerSitesDataAccess(ConfigService.GetConnectionString(dealerGroup));
         List<RetailerSite> retailers = await retailerSitesDataAccess.GetRetailerSites(dealerGroup);
         atCompetitorClient = new AutoTraderCompetitorClient(HttpClientFactoryService.HttpClientFactory, ConfigService.AutotraderApiKey, ConfigService.AutotraderApiSecret, ConfigService.AutotraderBaseURL);
         atTokenClient = new AutoTraderApiTokenClient(HttpClientFactoryService.HttpClientFactory, ConfigService.AutotraderApiKey, ConfigService.AutotraderApiSecret, ConfigService.AutotraderBaseURL);
         _bearerToken = (await atTokenClient.GetToken());
         Stopwatch stopwatch = new Stopwatch();
         stopwatch.Start();

         int advertiserId = retailers[0].RetailerId;

         await CollectAds(dealerGroup, isPrivate, stopwatch, advertiserId);
         List<string> allWebsiteStockIdentifiersSeen = allAds.Select(x => x.WebSiteStockIdentifier).ToList();

         AutotraderAdvertsDataAccess autotraderAdvertsDataAccess = new AutotraderAdvertsDataAccess(ConfigService.GetConnectionString(dealerGroup));


         //now tag snapshots to indicate if they are the latest
         //await autotraderAdvertsDataAccess.UpdateLatestAutotraderSnapForEachAd(isPrivate);
         logger.Info("Skipping latest snapshots");

         //now mark any ads as hasLeft and set the FirstSnapshotId and LastSnapshotId
         await autotraderAdvertsDataAccess.UpdateAutotraderAdvert_FirstLastHasLeft(isPrivate, startDate);
         logger.Info("Updated first last has left");

      }

      private async Task<bool> ShouldContinueRunningForTradeAds(ILog logger, DateTime startDate, DealerGroupName dealerGroup)
      {
         GlobalParamDataAccess globalParamDataAccess = new GlobalParamDataAccess(ConfigService.GetConnectionString(dealerGroup));
         var allParams = await globalParamDataAccess.GetAll(dealerGroup);
         var tradeAdsStartTime = allParams.Where(x => x.Description == "AutoTraderTradeAdsStartTime").FirstOrDefault().TextValue; //should be just time. i.e. 17:00

         if (DateTime.Now.Hour < int.Parse(tradeAdsStartTime.Split(':')[0]))
         { return false; }

         if (DateTime.Now.Hour == int.Parse(tradeAdsStartTime.Split(':')[0]) && DateTime.Now.Minute < int.Parse(tradeAdsStartTime.Split(':')[1]))
         { return false; }

         //if it has already been ran today, don't do it again
         AutotraderAdvertsDataAccess autotraderAdvertsDataAccess = new AutotraderAdvertsDataAccess(ConfigService.GetConnectionString(dealerGroup));
         bool tradeAdvertIdExists = autotraderAdvertsDataAccess.TradeAdvertIdExists(startDate, dealerGroup);
         if (tradeAdvertIdExists)
         { return false; }

         return true;

      }




      private async Task CollectAds(DealerGroupName dealerGroup, bool isPrivate, Stopwatch stopwatch, int advertiserId)
      {
         int startPrice = 0;
         int totalRequired = await FindHowManyWeNeed(advertiserId, startPrice, defaultPostcode, defaultRadius, isPrivate);
         logger.Info($"Need to get {totalRequired} vehicles");

         allAdsToSave = new List<AutotraderAdvertIncoming>();
         allAds = new List<AutotraderAdvertIncoming>();
         //AdsCount = 0;
         while (totalRequired > allAds.Count)
         {

            //The main loop where we grab the next 200 ads
            logger.Info($"Got {allAdsToSave.Count} in this save period, total {allAds.Count} so far.    Elapsed time is {stopwatch.Elapsed}s.  startPrice is {startPrice}");
            _bearerToken = await atTokenClient.CheckExpiryAndRegenerate(_bearerToken);
            int remaining = totalRequired - allAds.Count;
            List<AutotraderAdvertIncoming> vehicles = await GetTenPagesOfAds(advertiserId, isPrivate, startPrice, remaining, logger);

            //int totalRequiredNow = await FindHowManyWeNeed(advertiserId, startPrice, defaultPostcode, defaultRadius, isPrivate);


            //if within 200 vehicles of end, grab those then finish
            if (vehicles.Count < 200)
            {
               //we must have reached the end
               logger.Info($"Finishing with the final {vehicles.Count} vehicles");
               allAdsToSave.AddRange(vehicles);
               allAds.AddRange(vehicles);
               if (allAdsToSave.Count > 0)
               {
                  await SaveIncomingAds(isPrivate, advertiserId, dealerGroup);
                  allAdsToSave.Clear();
                  logger.Info("Final save is now completed.");
               }
               break;
            }

            //otherwise, more than 200, continue
            var finalPriceIn200List = vehicles.Last().SuppliedPrice;
            var completeRange = vehicles.Where(x => x.SuppliedPrice < finalPriceIn200List).ToList();
            if (completeRange.Count > 0)
            {
               //we fully obtained at least some vehicle price range, good
               //logger.Info($"Top price was {finalPriceIn200List}.  Found {completeRange.Count} vehicles below this ranging from £{startPrice} to £{completeRange.Last().SuppliedPrice}.  ");
               allAdsToSave.AddRange(completeRange);
               allAds.AddRange(completeRange);
               //logger.Info($"Now we need {totalRequiredNow} including this fetch.  We just saved {completeRange.Count} so next time we should use start price of {startPrice} and shld see {totalRequiredNow - completeRange.Count}");
               //AdsCount += completeRange.Count;

               startPrice = completeRange.Last().SuppliedPrice + 1;// completeRange.Last().SuppliedPrice;
               //logger.Info($"Next start price is {startPrice}");
               // Check if we need to save periodically
               if (allAdsToSave.Count > 2000)
               {
                  await SaveIncomingAds(isPrivate, advertiserId, dealerGroup);
               }
            }
            else
            {
               //--------------------------------------------------------------------------------
               //None of the 200 vehicles is < the final price. i.e. every vehicle is the same price.   We need to stratify on odometer also
               //--------------------------------------------------------------------------------
               startPrice = vehicles[0].SuppliedPrice; //set the start price to the first available vehicle, and drill into that by odometer


               //--------------------------------------------------------------------------------
               //Is all we can do is to now look in the odometer ranges for this price point
               //--------------------------------------------------------------------------------

               await LoopThroughOdometerRange(advertiserId, startPrice, isPrivate, dealerGroup);

               //now we have done the odometer loop, continue to the next price
               startPrice += 1;
            }

         }
         logger.Info("Finished CollectAds");

      }





      private async Task SaveIncomingAds(bool isPrivate, int advertiserId, DealerGroupName dealerGroup)
      {
         logger.Info($"Saving {allAdsToSave.Count} adverts periodically... total ads is {allAds.Count}");


         //defensive dedupe in case an advert appears more than once, for example if its price changed at exactly the wrong time
         var adsDeDuped = allAdsToSave
            .GroupBy(x => x.WebSiteStockIdentifier)
             .Select(g => g.Last())
             .ToList();

         if (adsDeDuped.Count != allAdsToSave.Count)
         {
            logger.Info($"Deduped {allAdsToSave.Count - adsDeDuped.Count} adverts");
         }

         //---------------------------------------------
         //create AutotraderAdverts
         //---------------------------------------------
         List<AutotraderAdvertLoad> adsForImportTable = new List<AutotraderAdvertLoad>();
         foreach (var ad in adsDeDuped)
         {
            var loadItem = new AutotraderAdvertLoad(ad, isPrivate);
            adsForImportTable.Add(loadItem);
         }
         AutotraderAdvertsDataAccess autotraderAdvertsDataAccess = new AutotraderAdvertsDataAccess(ConfigService.GetConnectionString(dealerGroup));
         var truncations = TruncaterService.Truncate(adsForImportTable);
         if (truncations.Count > 0)
         {
            foreach (var item in truncations)
            {
               Console.WriteLine(item);
            }
         }


         //note down existing in db
         List<string> incomingWebsiteIdentifiers = adsForImportTable.Select(x => x.WebSiteStockIdentifier).ToList();
         var adIdsAndHashes = await autotraderAdvertsDataAccess.GetAutotraderAdIdAndHashes(incomingWebsiteIdentifiers);
         var adIdsDict = adIdsAndHashes.ToDictionary(x => x.WebsiteStockIdentifier, x => x);
         Dictionary<string, int> stockIdentifierToId = new Dictionary<string, int>();
         foreach (var item in adIdsAndHashes)
         {
            stockIdentifierToId.Add(item.WebsiteStockIdentifier, item.AdId);
         }

         //determine new or changed ads
         List<AutotraderAdvertLoad> newIncomingAds = new();
         List<AutotraderAdvertLoad> changedIncomingAds = new();
         foreach (var ad in adsForImportTable)
         {
            if (adIdsDict.TryGetValue(ad.WebSiteStockIdentifier, out var existing))
            {
               if (existing.ContentHash != ad.ContentHash)
               {
                  //has changed
                  changedIncomingAds.Add(ad);
               }
               else
               {
                  //exists, not changed, no action
               }
            }
            else
            {
               //is new
               newIncomingAds.Add(ad);
            }
         }

         if (isPrivate)
         {
            //new! for the new ones, get their location
            await addLongLatToNewAds(newIncomingAds, advertiserId);
         }


         //new - check before we save
         foreach (var ad in newIncomingAds)
         {
            foreach (var p in ad.GetType()
                               .GetProperties()
                               .Where(pr => pr.PropertyType == typeof(string)))
            {
               var val = p.GetValue(ad) as string;
               var max = p.GetCustomAttribute<MaxLengthAttribute>()?.Length;

               if (max != null && val != null && val.Length > max)
               {
                  Console.WriteLine($"❌ {p.Name} too long ({val.Length}/{max})");
               }
               else if (max == null && val != null && val.Length > 4000)   // heuristic
               {
                  Console.WriteLine($"⚠ {p.Name} has no MaxLength and is {val.Length} chars");
               }
            }
         }



         //save the new ones
         var saved = await autotraderAdvertsDataAccess.SaveIncomingNewAdverts(newIncomingAds, logger);
         foreach (var item in saved)
         {
            stockIdentifierToId.Add(item.WebSiteStockIdentifier, item.Id);
         }

         //update the changed ones
         await autotraderAdvertsDataAccess.TruncateLoadTable();
         await autotraderAdvertsDataAccess.LoadToImportTable(changedIncomingAds);
         var changedItemMergeResults = await autotraderAdvertsDataAccess.CarryOutSQLMerge(isPrivate);

         //update the ids
         foreach (var ad in adsDeDuped)
         {
            ad.AutotraderAdvertId = stockIdentifierToId[ad.WebSiteStockIdentifier];
         }

         //---------------------------------------------
         //crud AutotraderFeatures
         //---------------------------------------------
         List<AutotraderFeatureIncoming> incomingFeatures = adsDeDuped.SelectMany(x => x.Features).ToList();
         var incomingUniqueFeatures = System.Linq.Enumerable
            .DistinctBy(incomingFeatures, x => x.UniqueId)
            .ToList();
         List<string> truncated = TruncaterService.Truncate(incomingUniqueFeatures);
         if (truncated.Count > 0)
         {
            foreach (var item in truncated)
            {
               Console.WriteLine(item);
            }
         }

         Dictionary<string, int> uniqueIdToId = await autotraderAdvertsDataAccess.UpsertAutotraderFeatures(incomingUniqueFeatures);

         foreach (var ad in adsDeDuped)
         {
            foreach (var feature in ad.Features)
            {
               feature.Id = uniqueIdToId[feature.UniqueId];
            }
         }


         //---------------------------------------------
         //crud mappings of features to ads
         //---------------------------------------------
         List<AutotraderAdvertFeatureIncoming> incomingMappings = new();
         foreach (var ad in adsDeDuped)
         {
            foreach (var feature in ad.Features)
            {
               incomingMappings.Add(new AutotraderAdvertFeatureIncoming((int)feature.Id, (int)ad.AutotraderAdvertId));
            }
         }
         Dictionary<string, int> mappingIdToId = await autotraderAdvertsDataAccess.UpsertAutotraderAdvertFeatureMappings(incomingMappings);


         //---------------------------------------------
         //create incoming snapshots and price changes
         //---------------------------------------------
         List<AutotraderAdvertSnapshotIncoming> incomingSnapshots = adsDeDuped.Select(x => new AutotraderAdvertSnapshotIncoming(x)).ToList();

         await autotraderAdvertsDataAccess.UpsertAutotraderSnapshots(incomingSnapshots);
         { }



         allAdsToSave.Clear();
         logger.Info("Periodic save completed. Continuing with data collection...");
      }

      private async Task addLongLatToNewAds(List<AutotraderAdvertLoad> newIncomingAds, int advertiserId)
      {
         AdvertLocationFinderService finderService = new AdvertLocationFinderService(atCompetitorClient);

         // Filter ads that need location data
         var adsNeedingLocation = newIncomingAds.Where(ad => ad.Longitude == null || ad.Latitude == null).ToList();

         // Process ads in batches of 5 in parallel
         const int batchSize = 5;
         for (int i = 0; i < adsNeedingLocation.Count; i += batchSize)
         {
            // Regenerate token once per batch, not per task
            _bearerToken = await atTokenClient.CheckExpiryAndRegenerate(_bearerToken);

            var batch = adsNeedingLocation.Skip(i).Take(batchSize);
            var tasks = batch.Select(async advert =>
            {
               try
               {
                  // logger.Info($"Finding long lat for {advert.VehicleReg}");
                  //string competitorLink = composeGetAdvertsUrl(1, isPrivate, advertiserId);
                  (decimal Long, decimal Lat) longLat = await finderService.GetAdLocation(advertiserId, advert.VehicleReg, _bearerToken, logger);
                  advert.Longitude = longLat.Long;
                  advert.Latitude = longLat.Lat;
               }
               catch (Exception ex)
               {
                  logger.Error($"addLongLatToNewAds: {ex.Message} on advert {advert.WebSiteStockIdentifier}");
               }
            });

            await Task.WhenAll(tasks);
         }
      }

      private async Task<List<AutotraderAdvertIncoming>> GetTenPagesOfAds(int advertiserId, bool isPrivate, int startPrice, int remainingToGet, ILog logger)
      {
         int pages = Math.Min(10, (int)Math.Ceiling(remainingToGet / 20.0));
         List<Task<VehicleListing>> tasks = new();

         for (int i = 1; i <= pages; i++)
         {
            string competitorLink = composeGetAdvertsUrl(startPrice, isPrivate, advertiserId);
            tasks.Add(atCompetitorClient.GetCompetitorByAdvertiser(competitorLink, i, _bearerToken.AccessToken, logger));
         }

         // Await all tasks, catching exceptions per-task
         VehicleListing[] results = await Task.WhenAll(
             tasks.Select(task => WrapWithCatch(task, logger))
         );


         var failed = results.Where(x => x == null || x.results == null);
         if (failed.Count() > 0)
         {
            logger.Error("Failed to fetch a VehicleListing page.");
         }

         // Filter out nulls (failed tasks)
         return results
             .Where(x => x != null && x.results != null)
             .SelectMany(x => x.results)
             .Select(x => new AutotraderAdvertIncoming(x))
             .ToList();
      }



      // Helper to wrap a task with exception handling
      private async Task<VehicleListing?> WrapWithCatch(Task<VehicleListing> task, ILog? logger)
      {
         try
         {
            return await task;
         }
         catch (Exception ex)
         {
            if (logger != null)
            {
               logger.Error("Failed to fetch a VehicleListing page.");
            }
            return null;
         }
      }

      private async Task<int> FindHowManyWeNeed(int advertiserId, int startPrice, string postcode, int radius, bool isPrivate)
      {
         string initialUrl = composeGetAdvertsUrl(startPrice, isPrivate, advertiserId);
         var initialCount = await atCompetitorClient.GetCompetitorByAdvertiser(initialUrl, 1, _bearerToken.AccessToken, logger);
         int remainingToGet = initialCount.totalResults;
         //remainingToGet = Math.Min(1300, remainingToGet);
         return remainingToGet;
      }



      private async Task LoopThroughOdometerRange(int advertiserId, int pricePoint, bool isPrivate, DealerGroupName dealerGroup)
      {
         //find out how many more ads we need for this price point (we already got 200)
         //string competitorLink = composeGetAdvertsForPricePointUrl(startPrice, startOdometer, advertiserId);
         //logger.Info($"Looping through odometer, price point is {pricePoint}.   TotalAds is {allAds.Count}");
         int? remainingToGetBasedOnOdometer = null; //we don't know this yet
         int weHaveGot = 0;
         int startOdometer = 0;
         int consecutiveErrors = 0;
         const int maxConsecutiveErrors = 3;

         while (remainingToGetBasedOnOdometer == null || remainingToGetBasedOnOdometer > weHaveGot)
         {
            try
            {
               int pages = 10;
               if (remainingToGetBasedOnOdometer != null)
               {
                  pages = Math.Min(10, (int)Math.Ceiling((int)remainingToGetBasedOnOdometer / 20.0));
               }
               List<Task<VehicleListing>> tasksOdometer = new List<Task<VehicleListing>>();
               for (int i = 1; i <= pages; i++)
               {
                  string pageLink = composeGetAdvertsForPricePointUrl(pricePoint, startOdometer, advertiserId, isPrivate);
                  tasksOdometer.Add(atCompetitorClient.GetCompetitorByAdvertiser(pageLink, i, _bearerToken.AccessToken, logger));
               }

               await Task.WhenAll(tasksOdometer);

               // Check for API errors first
               var errorTasks = tasksOdometer.Where(t => !string.IsNullOrEmpty(t.Result.errorMessage)).ToList();
               if (errorTasks.Any())
               {
                  consecutiveErrors++;
                  logger.Error($"API returned {errorTasks.Count} error responses. Consecutive error cycles: {consecutiveErrors}");

                  if (consecutiveErrors >= maxConsecutiveErrors)
                  {
                     logger.Error($"Too many consecutive error cycles ({maxConsecutiveErrors}). Stopping loop.");
                     break;
                  }

                  // If all tasks returned errors, break immediately
                  if (errorTasks.Count == tasksOdometer.Count)
                  {
                     logger.Error("All API calls returned errors. Breaking loop.");
                     break;
                  }
               }

               if (remainingToGetBasedOnOdometer == null)
               {
                  // Use the first successful response to get totalResults
                  var firstSuccessfulResult = tasksOdometer.FirstOrDefault(t => string.IsNullOrEmpty(t.Result.errorMessage));
                  if (firstSuccessfulResult != null)
                  {
                     remainingToGetBasedOnOdometer = firstSuccessfulResult.Result.totalResults; //each result will tell us how many ads are still required to get e.g. 300

                     if (remainingToGetBasedOnOdometer == 0)
                     {
                        logger.Info($"No results found for advertiserId {advertiserId}, pricePoint {pricePoint}");
                        break;
                     }
                  }
                  else
                  {
                     logger.Error("No successful responses to determine total results");
                     continue; // Try again
                  }
               }

               // Filter out error responses and get valid vehicles
               List<AutotraderAdvertIncoming> vehicles = tasksOdometer
                   .Where(t => string.IsNullOrEmpty(t.Result.errorMessage) && t.Result.results?.Any() == true)
                   .SelectMany(x => x.Result.results)
                   .Select(x => new AutotraderAdvertIncoming(x))
                   .ToList();

               // Check if we got any valid vehicles
               if (!vehicles.Any())
               {
                  consecutiveErrors++;
                  logger.Error($"No valid vehicles returned for advertiserId {advertiserId}, pricePoint {pricePoint}, startOdometer {startOdometer}. Consecutive empty responses: {consecutiveErrors}");

                  if (consecutiveErrors >= maxConsecutiveErrors)
                  {
                     logger.Error("Too many consecutive empty responses. Breaking loop.");
                     break;
                  }
                  continue; // Try next iteration
               }

               consecutiveErrors = 0; // Reset on successful data retrieval

               // Safe operations with null checks
               var finalVehicle = vehicles.LastOrDefault();
               if (finalVehicle == null)
               {
                  logger.Error("Unexpected: vehicles list is empty after validation");
                  break;
               }

               var completeRange = vehicles.Count < 200 ? vehicles : vehicles.Where(x => x.Odometer < finalVehicle.Odometer).ToList();

               if (!completeRange.Any())
               {
                  logger.Error("Complete range is empty after filtering");
                  break;
               }

               var lastVehicleWeGot = completeRange.Last().Odometer;
               allAdsToSave.AddRange(completeRange);
               allAds.AddRange(completeRange);
               weHaveGot += completeRange.Count; //e.g. we just got 160 more
               startOdometer = lastVehicleWeGot + 1;

               //logger.Info($"Got {completeRange.Count} vehicles. Total so far: {weHaveGot}/{remainingToGetBasedOnOdometer}");

               // Check if we need to save periodically
               if (allAdsToSave.Count > 2000)
               {
                  await SaveIncomingAds(isPrivate, advertiserId, dealerGroup);
               }

            }
            catch (Exception ex)
            {
               consecutiveErrors++;
               logger.Error($"Exception thrown in loop through odometer: {ex.Message}. Consecutive errors: {consecutiveErrors}");

               if (consecutiveErrors >= maxConsecutiveErrors)
               {
                  logger.Error($"Too many consecutive exceptions ({maxConsecutiveErrors}). Breaking loop.");
                  break;
               }

               // Add a small delay before retrying to avoid hammering the API
               await Task.Delay(1000);
            }
         }

         return;
      }







      private string composeGetAdvertsUrl(int startPrice, bool isPrivate, int advertiserId)
      {
         string channelString = isPrivate ? "Private" : "Trade";
         var sb = new StringBuilder();
         sb.Append($"{ConfigService.AutotraderBaseURL}/stock?");
         sb.Append($"searchType=competitor");
         sb.Append($"&advertiserId={advertiserId}");
         sb.Append($"&advertiserType={channelString}");
         sb.Append($"&pageSize=20");
         //sb.Append($"&vehicleType=Car");
         sb.Append($"&valuations=true");
         sb.Append($"&features=true");
         sb.Append($"&trendedValuations=true");
         sb.Append($"&highlights=false");
         sb.Append($"&minSuppliedPrice={startPrice}");
         sb.Append($"&sort=suppliedPriceAsc");
         //sb.Append("&standardMake=Volkswagen");
         //sb.Append("&standardModel=Arteon");

         var toReturn = sb.ToString();
         //logger.Info($"GetAdvertsUrl is {toReturn}");
         return toReturn;
      }



      private string composeGetAdvertsForPricePointUrl(int pricePoint, int startOdometer, int advertiserId, bool isPrivate)
      {
         string channelString = isPrivate ? "Private" : "Trade";
         var sb = new StringBuilder();
         sb.Append($"{ConfigService.AutotraderBaseURL}/stock?");
         sb.Append("searchType=competitor");
         sb.Append($"&advertiserId={advertiserId}");
         sb.Append($"&advertiserType={channelString}");
         sb.Append("&pageSize=20");
         //sb.Append($"&vehicleType=Car");
         sb.Append($"&valuations=true");
         sb.Append($"&features=true");
         sb.Append($"&trendedValuations=true");
         sb.Append($"&highlights=false");
         sb.Append($"&minSuppliedPrice={pricePoint}");
         sb.Append($"&maxSuppliedPrice={pricePoint}");
         sb.Append($"&minOdometerReadingMiles={startOdometer}");
         sb.Append("&sort=odometerReadingMilesAsc");
         //sb.Append("&standardMake=Volkswagen");
         //sb.Append("&standardModel=Arteon");
         //sb.Append("&standardMake=Vauxhall");

         var toReturn = sb.ToString();
         //logger.Info($"GetAdvertsUrl is {toReturn}");
         return toReturn;
      }














   }
}
