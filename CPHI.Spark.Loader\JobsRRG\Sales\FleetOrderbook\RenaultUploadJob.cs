﻿using CPHI.Repository;
using CPHI.Spark.Model;
using CPHI.Spark.Model.FltOrd;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.ViewModels.RRG;
using log4net;
using Microsoft.EntityFrameworkCore;
using Quartz;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace CPHI.Spark.Loader
{
   public class RenaultUploadJob : IJob
   {

      private static readonly ILog Logger = LogManager.GetLogger(typeof(RenaultUploadJob));

      private readonly IDapper dapper = new Dapperr();

      private int errorCount;
      private const int maxErrorLimit = 30;
      private const int maxDelayMinutes = 10;
      private LogMessage logMessage;

      //private string[] headers;

      private string fileSearch = "*Upload template - Renault*";
      //private string senderEmail;


      /*
Not sensitive
     */



      public async Task Execute(IJobExecutionContext context)
      {
         Stopwatch stopwatch = new Stopwatch();
         string errorMessage = string.Empty;
         stopwatch.Start();

         logMessage = new LogMessage();
         logMessage.DealerGroup_Id = 1;

         // Check for presence of lock, if so, return as already running
         if (LocksService.RenaultUploadJob) { return; }

         string fileToProcess = await GetFileToProcess();
         if (fileToProcess == null) { return; }

         // Create lock to prevent other instances running
         LocksService.RenaultUploadJob = true;

         if (ConfigService.isDev && !Environment.CurrentDirectory.Contains("bin\\Debug"))
         {
            System.Threading.Thread.Sleep(1000 * 60 * 5); //5 minute sleep to prevent concurrent load on database with production loader
         }

         CreateNewFileFoundMessage(logMessage, fileToProcess);

         try
         {
            string senderEmail = ExtractEmailBetweenSpecialCharacters(fileToProcess);
            string newFilepath = fileToProcess.Replace(".xlsx", "-p.xlsx");
            File.Move(fileToProcess, newFilepath); // append _processing to the file to prevent any other instances also processing these files

            List<UploadRow> inboundRows = ReadRowsFromFile(newFilepath);

            using (var db = new CPHIDbContext())
            {
               // We have rows to update
               if (inboundRows.Count > 0)
               {
                  List<RenaultOrderItem> renaultOrderItems = await db.RenaultOrderItems.ToListAsync();

                  inboundRows = AddRowIds(inboundRows, renaultOrderItems).Where(x => x.RowId != 0).ToList();

                  FleetOrderbookUpdatePropsParams fleetOrderbookUpdatePropsParams = new FleetOrderbookUpdatePropsParams();

                  fleetOrderbookUpdatePropsParams.IsRenault = true;
                  fleetOrderbookUpdatePropsParams.UpdatedRows = ConvertRows(inboundRows);

                  await UpdateProperties(db, fleetOrderbookUpdatePropsParams);

                  var confirmEmailList = ConfigService.emailsToConfirmFleetOrderbookLoadsTo.Split(',').ToList();
                  confirmEmailList.Add(senderEmail);

                  await AddComments(db, inboundRows, senderEmail);

                  if (!ConfigService.isDev)
                  {
                     await EmailerService.SendNotificationMail("Renault Fleet orderbook updated", "**Sent automatically from Spark**", confirmEmailList);
                  }
               }

               await PostFileProcessCleanup(db, newFilepath);
               await UpdateWebAppService.Trigger("FleetOrderbook", 2, "RRGUK");  //this tells the front end to drop its caches and rebuild.
            }

            stopwatch.Stop();
         }

         //Global error catcher
         catch (Exception err)
         {
            stopwatch.Stop();
            await GeneralFailureMsg(err);
         }

         finally
         {
            LocksService.RenaultUploadJob = false;

            Monitor.PostLogs.Model.MonitorMessage monitorMessage = new Monitor.PostLogs.Model.MonitorMessage()
            {
               Application = "Spark", // This value will not be used, instead the Id from config file will be posted. 
               Project = "Loader",
               Customer = "RRG",
               Environment = ConfigService.isDev == true ? "Dev" : "Prod",
               Task = this.GetType().Name,
               StartDate = DateTime.UtcNow.AddMilliseconds(-stopwatch.ElapsedMilliseconds),
               EndDate = DateTime.UtcNow,
               Status = logMessage.ErrorCount > 0 ? "Fail" : "Pass",
               Notes = logMessage.FailNotes,
               HTML = string.Empty
            };
            await Monitor.PostLogs.Monitor.AddMonitorMessage(monitorMessage);
         }

      }
      public static string ExtractEmailBetweenSpecialCharacters(string input)
      {
         // Define a regular expression pattern to match the substring between $ and .xlsx
         string pattern = @"\$(.+?)\.xlsx";

         // Use regex to find the match
         Match match = Regex.Match(input, pattern);
         if (match.Success)
         {
            string potentialEmail = match.Groups[1].Value;

            // Validate the extracted string as an email
            if (IsValidEmail(potentialEmail))
            {
               return potentialEmail;
            }
         }

         return null;
      }

      public static bool IsValidEmail(string email)
      {
         try
         {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
         }
         catch
         {
            return false;
         }
      }
      private List<UploadRow> AddRowIds(List<UploadRow> rows, List<RenaultOrderItem> renaultOrderItems)
      {
         foreach (var row in rows)
         {


            if (row.Chassis == "VF1RJB00970940211")
            {
               { }
            }

            // Search for COF first
            if (row.COF != 0)
            {
               var item = renaultOrderItems.FirstOrDefault(x => x.CustomerOrderNo == row.COF);

               if (item != null)
               {
                  row.RowId = item.Id;
               }
            }
            // Then search for Chassis
            else if (row.Chassis != null)
            {
               var item = renaultOrderItems.Where(x => x.Chassis == row.Chassis).OrderBy(x => x.CustomerOrderNo).LastOrDefault();

               if (item != null)
               {
                  row.RowId = item.Id;
               }
            }
         }

         return rows;
      }

      private List<FleetOrderbookUpdatedRow> ConvertRows(List<UploadRow> inputRows)
      {
         List<FleetOrderbookUpdatedRow> results = new List<FleetOrderbookUpdatedRow>();

         foreach (var row in inputRows)
         {
            FleetOrderbookUpdatedRow newRow = new FleetOrderbookUpdatedRow();

            if (row.RowId == 0) { continue; }

            newRow.RowId = row.RowId;
            newRow.Reg = row.Reg;
            newRow.DriverPackRequired = row.DriverPackRequired;
            newRow.DriverPackOrdered = row.DriverPackOrdered;
            newRow.DeliveryDate = row.DeliveryDate;
            newRow.StockCategory = row.StockCategory;

            newRow.AccountHandlerName = row.AccountHandlerName;
            newRow.ConvBldDte = row.ConvBldDte;
            newRow.ConvEstComplDte = row.ConvEstComplDte;

            newRow.ConverterName = row.ConverterName;
            newRow.ConverterBuildStartDate = row.ConverterBuildStartDate;
            newRow.ClearDeliveryDate = row.ClearDeliveryDate;

            // New properties added - 1st Sept 25
            newRow.PivgReference = row.PivgReference;
            newRow.DeliveryAgent = row.DeliveryAgent;

            results.Add(newRow);
         }

         return results;
      }


      private async Task<string> GetFileToProcess()
      {
         string[] allMatchingFiles;

         // Assuming ConfigService.incomingRoot is your directory path
         allMatchingFiles = await Task.Run(() =>
             Directory.EnumerateFiles(ConfigService.incomingRoot, fileSearch + "*.xlsx")
             .Where(file =>
             {
                var fileName = Path.GetFileName(file);
                // Exclude files explicitly ending with -p.xlsx
                if (Regex.IsMatch(fileName, @"-p\.xlsx$", RegexOptions.IgnoreCase))
                   return false;

                // Check for the pattern that includes $ before an email and ends with .xlsx
                // This regex accommodates for the $ sign before the email and allows multiple dots in the domain part
                return Regex.IsMatch(fileName, @"\$(\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*\.xlsx)$", RegexOptions.IgnoreCase);
             }).ToArray());

         if (allMatchingFiles.Length == 0) { CreateNoFilesFoundMessage(); return null; }// No files found.
         if (LocksService.RenaultUploadJob) { CentralLoggingService.ReportLock("RenaultUploadJob"); return null; } //return if already running

         string fileToProcess = allMatchingFiles[0];

         // Try opening the file, if fails, return (common problem is loader trying to open file whilst scraper is saving it).
         FileStream fs = File.Open(fileToProcess, FileMode.OpenOrCreate, FileAccess.ReadWrite, FileShare.None); fs.Close();

         // Data valid, continue
         return fileToProcess;
      }

      private void CreateNoFilesFoundMessage()
      {
         TimeSpan age = DateTime.UtcNow - PulsesService.RenaultUploadJob;

         if (age.Minutes > maxDelayMinutes)
         {
            PulsesService.RenaultUploadJob = DateTime.UtcNow;
            Logger.Info($@"[{DateTime.UtcNow}] | RenaultUploadJob | No files found matching pattern {fileSearch}");
         }
      }

      private void CreateNewFileFoundMessage(LogMessage logMessage, string fileToProcess)
      {
         logMessage.SourceDate = DateTime.UtcNow;
         logMessage.Job = this.GetType().Name;

         Logger.Info($@"[{DateTime.UtcNow}] New file found at {ConfigService.incomingRoot}.  Starting {fileToProcess}");   //update logger 
      }

      private async Task GeneralFailureMsg(Exception err)
      {
         logMessage.FailNotes = logMessage.FailNotes + $"General failure " + err.ToString();
         errorCount++;
         logMessage.FailNotes = $"{errorCount} errors " + "\r\n ------------------------ Errors ----------------------------- \r\n" + logMessage.FailNotes;

         await CentralLoggingService.ReportError("RenaultUploadJob", logMessage);
      }

      private List<UploadRow> ReadRowsFromFile(string newFilePath)
      {
         DataRowCollection allRows = GetDataFromFilesService.GetRowsExcelNew(newFilePath);
         List<UploadRow> results = new List<UploadRow>();
         Dictionary<string, int> headersLookup = new Dictionary<string, int>();

         int incomingProcessCount = 0;

         using (var db = new CPHIDbContext())
         {
            string[] headers = (from o in allRows.Cast<DataRow>().Skip(4).First().ItemArray
                                select o.ToString().ToUpper()).ToArray();

            headersLookup = SetHeaderColIndexDict(headers);

            foreach (DataRow row in allRows.Cast<DataRow>().Skip(8))
            {
               try
               {

                  string[] incomingRow = (from o in row.ItemArray
                                          select o.ToString()).ToArray();

                  var newRow = new UploadRow();

                  newRow.COF = GetIntFromString(incomingRow[headersLookup["COF"]]);
                  newRow.Chassis = (string)incomingRow[headersLookup["CHASSIS"]];

                  // No COF or Chassis, move to next one
                  if (newRow.COF == 0 && newRow.Chassis.Length < 1) { continue; }

                  newRow.StockCategory = (string)incomingRow[headersLookup["STOCK CATEGORY"]];
                  newRow.Reg = (string)incomingRow[headersLookup["VEHICLE REG"]];

                  newRow.DriverPackOrdered = GetNullableBool(incomingRow[headersLookup["DRIVER PACK ORDERED"]]);
                  newRow.DriverPackRequired = GetNullableBool(incomingRow[headersLookup["DRIVER PACK REQUIRED"]]);

                  string deliveryDate = incomingRow[headersLookup["DELIVERY DATE"]].ToUpper().Trim();

                  // If REMOVE is put in the delivery date field, it will clear the Delivery Date
                  if (deliveryDate.ToUpper() == "REMOVE")
                  {
                     newRow.DeliveryDate = null;
                     newRow.ClearDeliveryDate = true;
                  }
                  else
                  {
                     newRow.DeliveryDate = GetDataFromFilesService.GetDateTime(incomingRow[headersLookup["DELIVERY DATE"]]);
                     newRow.ClearDeliveryDate = false;
                  }

                  newRow.Comment = (string)incomingRow[headersLookup["COMMENT"]];

                  // Assume these rows are optional for now
                  newRow.AccountHandlerName = InterpretOptionalString(headersLookup, incomingRow, "ACCOUNT HANDLER NAME");
                  newRow.ConvEstComplDte = InterpretOptionalDate(headersLookup, incomingRow, "CONVERTER ESTIMATED COMPLETION DATE");
                  newRow.ConvBldDte = InterpretOptionalDate(headersLookup, incomingRow, "CONVERTER BUILD DATE");
                  newRow.ConverterName = InterpretOptionalString(headersLookup, incomingRow, "CONVERTER NAME");
                  newRow.ConverterBuildStartDate = InterpretOptionalDate(headersLookup, incomingRow, "CONVERTER BUILD START DATE");

                  // New properties added - 1st September
                  newRow.PivgReference = InterpretOptionalString(headersLookup, incomingRow, "PIVG ORDER REFERENCE");
                  newRow.DeliveryAgent = InterpretOptionalString(headersLookup, incomingRow, "DELIVERY AGENT");

                  results.Add(newRow);
                  // Logger.Info($@"[{incomingProcessCount}] | RenaultUploadJob ");

                  incomingProcessCount++;
               }

               catch (Exception err)
               {
                  if (errorCount < maxErrorLimit) logMessage.FailNotes += $" failed on adding item, Item: {incomingProcessCount} {err}";
                  errorCount++;
                  continue;
               }
            }

         }

         // Remove any duplicate data as this can cause issues on MERGE
         //List<FonName_Load> resultsNoDuplicates = results.GroupBy(x => x.Name).Select(x => x.First()).ToList();

         return results;
      }

      // Below two functions are to be used for fields that are optional in the template
      private string InterpretOptionalString(Dictionary<string, int> headersLookup, string[] incomingRow, string header)
      {
         if (headersLookup[header] == -1)
         {
            return null;
         }
         else
         {
            return (string)incomingRow[headersLookup[header]];
         }
      }

      private DateTime? InterpretOptionalDate(Dictionary<string, int> headersLookup, string[] incomingRow, string header)
      {
         if (headersLookup[header] == -1)
         {
            return null;
         }
         else
         {
            return GetDataFromFilesService.GetDateTime(incomingRow[headersLookup[header]]);
         }
      }

      private bool? GetNullableBool(string input)
      {
         if (string.IsNullOrEmpty(input))
         {
            return null;
         }
         return input == "y";
      }


      private int GetIntFromString(string inputString)
      {
         if (inputString.Length > 0 && IsStringNumeric(inputString))
         {
            return Int32.Parse(inputString);
         }

         return 0;
      }

      private bool IsStringNumeric(string str) { return str.All(char.IsDigit); }

      private Dictionary<string, int> SetHeaderColIndexDict(string[] headers)
      {
         Dictionary<string, int> headerDictionary;

         headerDictionary = new Dictionary<string, int>()
                {

                    {"COF", Array.IndexOf(headers, "COF")},
                    {"CHASSIS", Array.IndexOf(headers, "CHASSIS")},
                    {"STOCK CATEGORY", Array.IndexOf(headers, "STOCK CATEGORY")},
                    {"VEHICLE REG", Array.IndexOf(headers, "VEHICLE REG")},
                    {"DRIVER PACK REQUIRED", Array.IndexOf(headers, "DRIVER PACK REQUIRED")},
                    {"DRIVER PACK ORDERED", Array.IndexOf(headers, "DRIVER PACK ORDERED")},
                    {"DELIVERY DATE", Array.IndexOf(headers, "DELIVERY DATE")},
                    {"COMMENT", Array.IndexOf(headers, "COMMENT")},
                    {"ACCOUNT HANDLER NAME", Array.IndexOf(headers, "ACCOUNT HANDLER NAME")},
                    {"CONVERTER ESTIMATED COMPLETION DATE", Array.IndexOf(headers, "CONVERTER ESTIMATED COMPLETION DATE")},
                    {"CONVERTER BUILD DATE", Array.IndexOf(headers, "CONVERTER BUILD DATE")},
                    {"CONVERTER NAME", Array.IndexOf(headers, "CONVERTER NAME")},
                    {"CONVERTER BUILD START DATE", Array.IndexOf(headers, "CONVERTER BUILD START DATE")},
                    {"PIVG ORDER REFERENCE", Array.IndexOf(headers, "PIVG ORDER REFERENCE")},
                    {"DELIVERY AGENT", Array.IndexOf(headers, "DELIVERY AGENT")},

                };


         return headerDictionary;


      }


      public async Task PostFileProcessCleanup(CPHIDbContext db, string newFilePath)
      {
         try
         {

            //move file
            File.Move(newFilePath, newFilePath.Replace(@"\inbound", @"\processed").Replace("p.xlsx", $"processed{DateTime.UtcNow.ToString("yyyyMMdd_HHmmss")}.xlsx"));

            //log: errors
            if (errorCount > 0)
            {
               // We have errors so use the reporter
               logMessage.FailNotes = $"{errorCount} errors " + "\r\n ------------------------ Errors ----------------------------- \r\n" + logMessage.FailNotes;
               await CentralLoggingService.ReportError("RenaultUploadJob", logMessage);
            }

            //log: no errors
            else
            {
               // Completed succesfully, save log and remove lockfile
               logMessage.FinishDate = DateTime.UtcNow;
               logMessage.IsCompleted = true;
               logMessage.DealerGroup_Id = 1;
               db.LogMessages.Add(logMessage);
               db.SaveChanges();
            }
         }

         catch (Exception err)
         {
            errorCount++;
            logMessage.FailNotes = logMessage.FailNotes + " FAILED moving file and logging to server" + err.ToString();
            logMessage.FinishDate = DateTime.UtcNow;
            logMessage.ErrorCount = errorCount;
            logMessage.DealerGroup_Id = 1;
            db.LogMessages.Add(logMessage);
            await CentralLoggingService.ReportError("RenaultUploadJob", logMessage);
         }

      }

      public async Task AddComments(CPHIDbContext db, List<UploadRow> rows, string senderEmail)
      {
         List<FleetOrderComment> comments = new List<FleetOrderComment>();

         int senderId = db.People.Where(x => x.Email == senderEmail).FirstOrDefault().Id;

         foreach (var row in rows)
         {
            if (row.Comment != null && row.Comment != "")
            {
               FleetOrderComment comment = new FleetOrderComment();
               comment.RenaultOrderItem_Id = row.RowId;
               comment.Text = row.Comment;
               comment.Date = DateTime.Now;
               comment.PersonId = senderId;
               comments.Add(comment);
            }

         }

         await db.FleetOrderComments.AddRangeAsync(comments);
         await db.SaveChangesAsync();
      }

      public async Task UpdateProperties(CPHIDbContext db, FleetOrderbookUpdatePropsParams parms)
      {

         // ------------------------------------------------------------
         // Step 1 - Based on the RenaultOrderItemIds or
         // NissanOrderItemIds, get the associated OrderTrackingItemIds
         // ------------------------------------------------------------
         string query = GenerateRenaultQuery(parms);
         Dictionary<int, FleetOrderbookOrderItemAndTrackingItemId> itemLookup = await GetOrderTrackingItemIdLookup(parms, query);


         // ------------------------------------------------------------
         // Step 2 - Do the create or update
         // ------------------------------------------------------------
         await CreateOrUpdateOrderTrackingItems(db, parms, itemLookup);
         await db.SaveChangesAsync();
      }


      private static string GenerateRenaultQuery(FleetOrderbookUpdatePropsParams parms)
      {
         return $@"SELECT
                ro.Id as OrderItemId,
                oti.Id as OrderTrackingItemId
                FROM fltord.RenaultOrderItems ro
                LEFT JOIN fltord.OrderTrackingItems oti on oti.RenaultOrderItemId = ro.Id
                WHERE ro.Id IN ({string.Join(',', parms.UpdatedRows.Select(x => x.RowId).ToList())})";
      }

      private async Task<Dictionary<int, FleetOrderbookOrderItemAndTrackingItemId>> GetOrderTrackingItemIdLookup(FleetOrderbookUpdatePropsParams parms, string query)
      {
         IEnumerable<FleetOrderbookOrderItemAndTrackingItemId> existingItems = await dapper.GetAllAsync<FleetOrderbookOrderItemAndTrackingItemId>(query, null, System.Data.CommandType.Text);
         Dictionary<int, FleetOrderbookOrderItemAndTrackingItemId> itemLookup = existingItems.ToDictionary(x => x.OrderItemId);
         return itemLookup;
      }

      private async Task CreateOrUpdateOrderTrackingItems(CPHIDbContext db, FleetOrderbookUpdatePropsParams parms, Dictionary<int, FleetOrderbookOrderItemAndTrackingItemId> itemLookup)
      {
         FleetOrderbookTrackingItemUpdateKeyObjects keyObjects = new FleetOrderbookTrackingItemUpdateKeyObjects()
         {
            //fonNames = await db.FonNames.ToListAsync(),
            stockCategoryItems = await db.StockCategoryItems.ToListAsync(),
            alarmDataItems = await db.AlarmDataItems.ToListAsync(),
         };

         List<OrderTrackingItem> newItems = new List<OrderTrackingItem>();

         foreach (var row in parms.UpdatedRows)
         {
            int? orderTrackingItemId = itemLookup[row.RowId].OrderTrackingItemId;

            if (orderTrackingItemId == null)
            {
               //must require a new item
               var newItem = new OrderTrackingItem();
               if (parms.IsRenault) { newItem.RenaultOrderItemId = row.RowId; }
               else { newItem.NissanOrderItemId = row.RowId; }
               UpdateOrderTrackingItemProps(row, newItem, keyObjects);
               if (newItem.DriverPackOrdered == null) { newItem.DriverPackOrdered = false; }
               if (newItem.DriverPackRequired == null) { newItem.DriverPackRequired = false; }
               if (newItem.IsHidden == null) { newItem.IsHidden = false; }
               newItems.Add(newItem);
            }
            else
            {
               //we have an orderTrackingItem
               var orderTrackingItem = await db.OrderTrackingItems.AsTracking().FirstAsync(x => x.Id == orderTrackingItemId);
               UpdateOrderTrackingItemProps(row, orderTrackingItem, keyObjects);
            }

            db.OrderTrackingItems.AddRange(newItems);
         }



      }


      private static void UpdateOrderTrackingItemProps(FleetOrderbookUpdatedRow row, OrderTrackingItem orderTrackingItem, FleetOrderbookTrackingItemUpdateKeyObjects keyObjects)
      {
         //string fields
         if (!string.IsNullOrEmpty(row.SalesPerson))
         {
            orderTrackingItem.SalesPerson = row.SalesPerson;
         }
         if (!string.IsNullOrEmpty(row.EndUser))
         {
            orderTrackingItem.EndUser = row.EndUser;
         }
         if (!string.IsNullOrEmpty(row.CustomerOrderNo))
         {
            orderTrackingItem.CustomerOrderNo = row.CustomerOrderNo;
         }
         if (!string.IsNullOrEmpty(row.DeliveryDepot))
         {
            orderTrackingItem.DeliveryDepot = row.DeliveryDepot;
         }
         if (!string.IsNullOrEmpty(row.FonCode))
         {
            orderTrackingItem.FonCode = row.FonCode;
         }
         if (!string.IsNullOrEmpty(row.Reg))
         {
            orderTrackingItem.Reg = row.Reg;
         }
         if (!string.IsNullOrEmpty(row.AccountHandlerName))
         {
            orderTrackingItem.AccountHandlerName = row.AccountHandlerName;
         }
         if (!string.IsNullOrEmpty(row.ConverterName))
         {
            orderTrackingItem.ConverterName = row.ConverterName;
         }

         // New columns - added 1st Sept 2025
         if (!string.IsNullOrEmpty(row.PivgReference))
         {
            orderTrackingItem.PivgReference = row.PivgReference;
         }
         if (!string.IsNullOrEmpty(row.DeliveryAgent))
         {
            orderTrackingItem.DeliveryAgent = row.DeliveryAgent;
         }

         if (!string.IsNullOrEmpty(row.AlarmData))
         {
            var matchingAlarmDataItem = keyObjects.alarmDataItems.FirstOrDefault(x => x.AlarmData == row.AlarmData);
            if (matchingAlarmDataItem != null)
            {
               orderTrackingItem.AlarmDataItemId = matchingAlarmDataItem.Id;
            }
         }
         if (!string.IsNullOrEmpty(row.StockCategory))
         {
            var matchingStockCategory = keyObjects.stockCategoryItems.FirstOrDefault(x => x.StockCategory == row.StockCategory);
            if (matchingStockCategory != null)
            {
               orderTrackingItem.StockCategoryItemId = matchingStockCategory.Id;
            }
         }

         // Date fields
         if (row.DeliveryDate != null)
         {
            orderTrackingItem.DeliveryDate = row.DeliveryDate;
         }

         // If ClearDeliveryDate is set to true, clear
         if (row.ClearDeliveryDate)
         {
            orderTrackingItem.DeliveryDate = null;
         }

         if (row.InvoiceDate != null)
         {
            orderTrackingItem.InvoiceDate = row.InvoiceDate;
         }
         if (row.ConvBldDte != null)
         {
            orderTrackingItem.ConvBldDte = row.ConvBldDte;
         }
         if (row.ConvEstComplDte != null)
         {
            orderTrackingItem.ConvEstComplDte = row.ConvEstComplDte;
         }
         if (row.ConverterBuildStartDate != null)
         {
            orderTrackingItem.ConverterBuildStartDate = row.ConverterBuildStartDate;
         }

         //bool fields
         if (row.DriverPackOrdered != null)
         {
            orderTrackingItem.DriverPackOrdered = (bool)row.DriverPackOrdered;
         }
         if (row.DriverPackRequired != null)
         {
            orderTrackingItem.DriverPackRequired = (bool)row.DriverPackRequired;
         }
         if (row.IsHidden != null)
         {
            orderTrackingItem.IsHidden = (bool)row.IsHidden;
         }

         //other
         if (orderTrackingItem.RemovedDate == null && row.IsRemoved)
         {
            //we wish to set the orderTrackingItem to removed
            orderTrackingItem.RemovedDate = DateTime.Now.Date;
         }
         else if (!row.IsRemoved)
         {
            //we wish to set the orderTrackingItem to not removed.
            orderTrackingItem.RemovedDate = null;
         }





      }

   }


}