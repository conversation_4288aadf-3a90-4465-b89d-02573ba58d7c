﻿using CPHI.Spark.Model;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using System;
using System.Collections.Generic;
using System.Data;
using System.Globalization;
using System.IO;
using System.Linq;

namespace CPHI.Spark.Loader.Services.GenericStockLoader
{

   public class EMGMantles_StockJobService : IGenericStockJobServiceParams
   {

      public int RowsToSkip { get; set; }
      public bool IncludePreviousUses { get; set; }
      public bool IncludeAccountStatuses { get; set; }
      public DealerGroupName DealerGroupName { get; set; }
      public string FileExt { get; set; }
      public string DbConnectionName { get; set; }
      public string DbConnectionString { get; set; }
      public string[] AllMatchingFiles { get; set; }
      public string FinalSPToRun { get; set; }
      public string? TabName { get; set; }
      public bool TriggerUpdate { get; set; }
      public string JobName { get; set; }
      public string OnlyForSiteIds { get; set; }

      public EMGMantles_StockJobService(string incomingRoot)
      {
         GetMatchingFilesAndImportParams(incomingRoot);
      }


      public void GetMatchingFilesAndImportParams(string incomingRoot)
      {
         DealerGroupName = DealerGroupName.EMGGroup;
         RowsToSkip = 0;
         JobName = string.Concat(DealerGroupName.EMGGroup.ToString() + "Mantles", "StocksJob");
         IncludePreviousUses = false;
         IncludeAccountStatuses = false;
         FileExt = ".csv";
         DbConnectionString = ConfigService.autopriceConnectionString;
         DbConnectionName = "AutoPriceConnection";
         AllMatchingFiles = Directory.GetFiles(incomingRoot, $"*MantlesStock*");
         OnlyForSiteIds = "110";
         TriggerUpdate = true;

      }

      public DataTable ConvertToDataTable(List<Stock> toReturn)
      {
         var dataTable = toReturn.ToDataTable();

         dataTable.Columns.Remove("PreviousSite");
         dataTable.Columns.Remove("Site");
         dataTable.Columns.Remove("DisposalRoute");
         dataTable.Columns.Remove("ProgressCode");
         dataTable.Columns.Remove("VehicleType");
         dataTable.Columns.Remove("AccountStatus");
         dataTable.Columns.Remove("PreviousUse");
         dataTable.Columns.Remove("VehicleAdvert");

         return dataTable;
      }


      public Stock ConvertRowToStock(List<string> rowCells, GenericStockLoaderDbLookups lookups, Dictionary<string, int> headerDictionary)
      {

         Stock newRow = new Stock();

         // Everything is at Mantles Royston currently
         newRow.Site_Id = 110;
         
         newRow.Reg = (rowCells[headerDictionary["REGISTRATIONNUMBER"]] ?? "").Replace(" ", "").Trim();
         newRow.Chassis = rowCells[headerDictionary["VIN"]]; //

         newRow.StockNumber = ExtractNumbers(rowCells[headerDictionary["STOCKNUMBER"]]);
         newRow.StockNumberFull = rowCells[headerDictionary["STOCKNUMBER"]]; //

         string mileAge = rowCells[headerDictionary["ODOMETER"]];
         newRow.Mileage = mileAge == "" ? 0 : int.Parse(rowCells[headerDictionary["ODOMETER"]]); //

         newRow.IsVatQ = RowInterpretationService.GetVatQualifying(rowCells[headerDictionary["ISQUALIFYING"]]); //
         newRow.Colour = rowCells[headerDictionary["COLOUR"]];
         newRow.Fuel = rowCells[headerDictionary["FUELTYPE"]]; //

         DateTime sqlMinDate = new DateTime(1753, 1, 1); // SQL Server min DateTime
         DateTime sqlMaxDate = new DateTime(9999, 12, 31); // SQL Server max DateTime

         string regDateString = rowCells[headerDictionary["REGISTRATIONDATE"]];

         string[] dateFormats =
         {
                "dd/MM/yyyy HH:mm",        // e.g., 30/09/2023 00:00
                "dd/MM/yyyy",              // e.g., 30/09/2023
                "yyyy-MM-dd HH:mm:ss",     // e.g., 2023-09-30 00:00:00
                "dd/MM/yyyy HH:mm:ss",     // ✅ Added for 30/09/2023 00:00:00
                "yyyy-MM-dd'T'HH:mm:ss"    // ✅ Handles ISO-8601 (e.g., 2023-09-30T00:00:00)
            };

         if (string.IsNullOrWhiteSpace(regDateString))
         {
            newRow.RegDate = null;
         }
         else if (DateTime.TryParseExact(regDateString, dateFormats,
                                        CultureInfo.InvariantCulture,
                                        DateTimeStyles.None, out DateTime parsedDate))
         {
            if (parsedDate < sqlMinDate || parsedDate > sqlMaxDate)
            {
               newRow.RegDate = null; // Prevent overflow exception
            }
            else
            {
               newRow.RegDate = parsedDate;
            }
         }
         else
         {
            newRow.RegDate = null; // Handle invalid format gracefully
         }

         newRow.Make = rowCells[headerDictionary["MAKE"]];
         newRow.Model = rowCells[headerDictionary["MODEL"]];
         newRow.Doors = rowCells[headerDictionary["DOORS"]]
             ;
         //DateTime stockDate = DateTime.Parse(rowCells[headerDictionary["AVAILABLE FOR SALE"]]); //
         //newRow.StockDate = stockDate; //

         //DateTime branchStockDate = DateTime.Parse(rowCells[headerDictionary["AVAILABLE FOR SALE"]]); //
         //newRow.BranchStockDate = branchStockDate; //       

         newRow.Purchased = RowInterpretationService.GetDecimal(rowCells[headerDictionary["BASICPRICE"]]);
         newRow.OriginalPurchasePrice = RowInterpretationService.GetDecimal(rowCells[headerDictionary["BASICPRICE"]]);
         newRow.Selling = RowInterpretationService.GetDecimal(rowCells[headerDictionary["RETAILPRICE"]]);

         newRow.Transmission = rowCells[headerDictionary["TRANSMISSION"]];

         int dis = RowInterpretationService.GetInt(rowCells[headerDictionary["DAYSINSTOCK"]]);
         int groupDis = RowInterpretationService.GetInt(rowCells[headerDictionary["DAYSINSTOCKGROUP"]]);

         newRow.Dis = dis;

         DateTime today = DateTime.Now;
         newRow.BranchStockDate = today.AddDays(-dis);
         newRow.StockDate = today.AddDays(-groupDis);

         // These FKs will always be Retail for now
         int disposalRouteId = lookups.disposalRoutes.Where(x => x.Description == "Retail").First().Id;
         newRow.DisposalRoute_Id = disposalRouteId;

         string supplierOrderNumber = rowCells[headerDictionary["SUPPLIERORDERNUMBER"]];
         string vehicleType = "Retail";

         // Check if SOR or Approved (no overlap between the two)
         if(IsSORVehicleType(supplierOrderNumber))
         {
            vehicleType = "SOR";
         }
         else if(IsApprovedVehicleType(supplierOrderNumber))
         {
            vehicleType = "Approved";
         }
         else if (IsDemoVehicleType(supplierOrderNumber))
         {
            vehicleType = "Demo";
         }

         int vehTypeId = lookups.vehicleTypes.Where(x => x.Description == vehicleType).First().Id;
         newRow.VehicleType_Id = vehTypeId;

         newRow.LastUpdatedDate = DateTime.Now;

         return newRow;
      }

      private static bool IsDemoVehicleType(string supplierOrderNumber)
      {
         return !string.IsNullOrEmpty(supplierOrderNumber) &&
                supplierOrderNumber.Length >= 4 &&
                supplierOrderNumber.Substring(0, 4).Equals("DEMO", StringComparison.OrdinalIgnoreCase);
      }

      private static bool IsApprovedVehicleType(string supplierOrderNumber)
      {
         return !string.IsNullOrEmpty(supplierOrderNumber) &&
                supplierOrderNumber.Length >= 3 &&
                supplierOrderNumber.Substring(0, 3).Equals("APR", StringComparison.OrdinalIgnoreCase);
      }

      private static bool IsSORVehicleType(string supplierOrderNumber)
      {
         return !string.IsNullOrEmpty(supplierOrderNumber) &&
                supplierOrderNumber.Length >= 3 &&
                supplierOrderNumber.Substring(0, 3).Equals("SOR", StringComparison.OrdinalIgnoreCase);
      }



      private static (string firstWord, string restOfString) GetFirstWordAndRest(string input)
      {
         if (string.IsNullOrWhiteSpace(input))
         {
            return (string.Empty, string.Empty); // Return empty strings if input is null, empty, or whitespace
         }

         string[] words = input.Split(new[] { ' ' }, 2, StringSplitOptions.RemoveEmptyEntries);
         string firstWord = words.Length > 0 ? words[0] : string.Empty;
         string restOfString = words.Length > 1 ? words[1] : string.Empty;

         return (firstWord, restOfString);
      }

      private int ExtractNumbers(string input)
      {
         string numericString = new String(input.Where(char.IsDigit).ToArray());
         return int.Parse(numericString);
      }


      public Dictionary<string, int> BuildHeaderDictionary(List<string> headers)
      {
         Dictionary<string, int> result = new Dictionary<string, int>
{
                { "STOCKNUMBER", headers.IndexOf("STOCKNUMBER") },
                { "REGISTRATIONNUMBER", headers.IndexOf("REGISTRATIONNUMBER") },
                { "VIN", headers.IndexOf("VIN") },
                { "ISQUALIFYING",headers.IndexOf("ISQUALIFYING") },
                { "RETAILPRICE", headers.IndexOf("RETAILPRICE") },
                { "COLOUR", headers.IndexOf("COLOUR") },
                { "ODOMETER", headers.IndexOf("ODOMETER") },
                { "VEHICLECODE", headers.IndexOf("VEHICLECODE") },
                { "BASICPRICE", headers.IndexOf("BASICPRICE") },
                { "FUELTYPE", headers.IndexOf("FUELTYPE") },
                { "DOORS", headers.IndexOf("DOORS") },
                { "LOCATION", headers.IndexOf("LOCATION") },
                { "MAKE", headers.IndexOf("MAKE") },
                { "MODEL", headers.IndexOf("MODEL") },
                { "PREVIOUSOWNERS", headers.IndexOf("PREVIOUSOWNERS") },
                { "SPECIFICATION", headers.IndexOf("SPECIFICATION") },
                { "VEHICLETYPE", headers.IndexOf("VEHICLETYPE") },
                { "DAYSINSTOCKGROUP", headers.IndexOf("DAYSINSTOCKGROUP") },
                { "INTERNETPRICE", headers.IndexOf("INTERNETPRICE") },
                { "MANUFACTURERRECOMMENDEDRETAILPRICE", headers.IndexOf("MANUFACTURERRECOMMENDEDRETAILPRICE") },
                { "TRANSMISSION", headers.IndexOf("TRANSMISSION") },
                { "DAYSINSTOCK", headers.IndexOf("DAYSINSTOCK") },
                { "SUPPLIERORDERNUMBER", headers.IndexOf("SUPPLIERORDERNUMBER") },
                { "REGISTRATIONDATE", headers.IndexOf("REGISTRATIONDATE") },
                { "STATUS", headers.IndexOf("STATUS") }
            };


         return result;
      }

   }
}
