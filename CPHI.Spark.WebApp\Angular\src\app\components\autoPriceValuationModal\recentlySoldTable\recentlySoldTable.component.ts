import { Component, Input, OnInit } from '@angular/core';
import { GridApi, GridOptions, GridReadyEvent } from 'ag-grid-community';
import { CphPipe } from 'src/app/cph.pipe';
import { LeavingVehicleItem } from 'src/app/model/LeavingVehicleItem';
import { AutoTraderAdvertImage } from 'src/app/_cellRenderers/autoTraderAdvertImage.component';
import { ColumnTypesService } from 'src/app/services/columnTypes.service';
import { AGGridMethodsService } from 'src/app/services/agGridMethods.service';
import { ConstantsService } from 'src/app/services/constants.service';

@Component({
  selector: 'recentlySoldTable',
  templateUrl: './recentlySoldTable.component.html',
  styleUrls: ['./recentlySoldTable.component.scss']
})
export class RecentlySoldTableComponent implements OnInit {
  @Input() data: LeavingVehicleItem[];
  @Input() fullHeight: boolean;
  gridOptions: GridOptions;
  gridApi: Grid<PERSON>pi;
  resizeObserver: ResizeObserver;

  constructor(
    public cphPipe: CphPipe,
    private colTypesService: ColumnTypesService,
    public gridHelpersService: AGGridMethodsService,
    public constants: ConstantsService
  ) { }

  ngOnInit(): void {
    this.setGridDefinitions();

    const table = document.getElementById('recentlySoldTable');

    this.resizeObserver = new ResizeObserver(entries => {
      if (this.gridApi) {
        setTimeout(() => {
          this.gridApi.sizeColumnsToFit();
        }, 250)
      }
    });

    if (table) {
      this.resizeObserver.observe(table);
    }
  }

  ngOnDestroy(): void {
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
  }

  setGridDefinitions() {
    this.gridOptions = {
      
      defaultColDef: {
        resizable: true,
        sortable: true,
        filterParams: {
          applyButton: false,
          clearButton: true,
          cellHeight: this.gridHelpersService.getFilterListItemHeight()
        },
        autoHeight: true,
        headerComponentParams: {
          showPinAndRemoveOptions: false
        },
        autoHeaderHeight: true
      },
      columnTypes:  {...this.colTypesService.provideColTypes([]) },
      
      rowSelection: 'multiple',
      rowData: this.data,
      columnDefs: [
        { headerName: 'Sale Date', colId: 'RemovedDate', field: 'RemovedDate', type: 'date',  width: 30,sort:'desc' },
        { headerName: 'Photo', colId: 'ImageURLs', field: 'ImageURL', type: 'special', cellRenderer: AutoTraderAdvertImage, width: 30 },
        { headerName: 'Site', colId: 'RetailerSiteName', field: 'RetailerSiteName', type: 'label', width: 50 },
        { headerName: 'Derivative', colId: 'MakeModelDerivative', field: 'MakeModelDerivative', type: 'label', width: 80 },
        { headerName: 'Days Listed', colId: 'DaysListed', field: 'DaysListed', type: 'number', width: 20 },
        { headerName: 'Final Price Position %', colId: 'LastPP', field: 'LastPP', type: 'percent1dp', width: 30 }
      ],
      pinnedTopRowData:this.makePinnedTopRowData(),
      onGridReady: (event: GridReadyEvent) => this.onGridReady(event),
      getRowHeight: (params) => {
        if (params.node.rowPinned) {
          return this.gridHelpersService.getRowPinnedHeight();
        } else {
          return this.gridHelpersService.getStandardHeight();
        }
      }
    }
  }

  makePinnedTopRowData(){
    let newRow = {
      
      RetailerSiteName:null,
      MakeModelDerivative: 'Average',
      DaysListed:0,
      LastPP:0
    } as LeavingVehicleItem
    this.data.forEach(item=>{
      newRow.DaysListed += item.DaysListed;
      newRow.LastPP += item.LastPP;
    })
    newRow.DaysListed = newRow.DaysListed/this.data.length;
    newRow.LastPP = newRow.LastPP/this.data.length;
    return [newRow]
  }

  onGridReady(event: GridReadyEvent) {
    this.gridApi = event.api;
    this.gridApi.sizeColumnsToFit();
  }
}
