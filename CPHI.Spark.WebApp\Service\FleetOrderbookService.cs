﻿
using CPHI.Spark.Model.FltOrd;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.ViewModels.RRG;
using CPHI.Spark.WebApp.DataAccess;
using System;
using System.Collections.Generic;
using System.Reflection;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using OfficeOpenXml;
using System.IO;
using Task = System.Threading.Tasks.Task;
using CPHI.Spark.Model;
using System.Linq;


namespace CPHI.Spark.WebApp.Service
{
   public interface IFleetOrderbookService
   {
      Task<int> AddNewComment(FleetOrderbookNewCommentParams parms, Model.DealerGroupName dealerGroup);
      Task<IEnumerable<FleetOrderComment>> AddNewComments(FleetOrderbookNewCommentsParams parms);
      Task DeleteComment(int commentId, Model.DealerGroupName dealerGroup);
      Task<IEnumerable<FleetOrderbookLastUpdatedDropdown>> GetLastUpdatedDates(Model.DealerGroupName dealerGroup);
      Task DeleteTableState(string state, Model.DealerGroupName dealerGroup);
      Task<IEnumerable<FleetOrderbookEditableOption>> GetEditableOptions(Model.DealerGroupName dealerGroup);
      Task<FleetOrderbookRowCollection> GetFleetOrderbookRows(string brand, bool includeHidden, FleetOrderbookSummaryRow filterChoices, DateTime? includeRemovedAfter, Model.DealerGroupName dealerGroup);
      Task<FleetOrderbookTableState> GetLastLoadedTableState(bool isRenault);
      Task<IEnumerable<string>> GetTableStateLabels(bool isRenault);
      Task<FleetOrderTableState> SaveTableState(FleetOrderbookTableStateParams parms, Model.DealerGroupName dealerGroup);
      Task UpdateComment(FleetOrderbookUpdatedCommentParams parms, Model.DealerGroupName dealerGroup);
      Task UpdateProperties(FleetOrderbookUpdatePropsParams parms, FleetOrderbookSummaryRow filterChoices, Model.DealerGroupName dealerGroup);
      Task<FleetOrderbookTableStateParams> GetTableState(string label, bool isRenault);
      Task<IEnumerable<FonName>> GetFonNames();
      Task<FonName> UpdateFonName(FonName fonName);
      Task<FonName> AddFonName(FonName fonName);
      Task<FonName> DeleteFonName(FonName fonName);
      Task<IEnumerable<FleetOrderbookSummaryRow>> GetFleetOrderbookSummaryRows(string brand);
      Task<FileStreamResult> DownloadUploadTemplate(ExcelChoices prams, int userId);
   }

   public class FleetOrderbookService : IFleetOrderbookService
   {
      private readonly IUserService userService;
      private readonly IFleetOrderbookDataAccess fleetOrderbookDataAccess;
      private readonly IFleetOrderbookCache fleetOrderbookCache;


      public FleetOrderbookService(IUserService userService, IFleetOrderbookDataAccess fleetOrderbookDataAccess, IFleetOrderbookCache fleetOrderbookCache)
      {
         this.userService = userService;
         this.fleetOrderbookDataAccess = fleetOrderbookDataAccess;
         this.fleetOrderbookCache = fleetOrderbookCache;
      }

      //private int userId;
      public async Task<IEnumerable<FleetOrderbookLastUpdatedDropdown>> GetLastUpdatedDates(Model.DealerGroupName dealerGroup)
      {
         return await fleetOrderbookDataAccess.GetLastUpdatedDates(userService.GetUserDealerGroupName());
      }

      public async Task<FleetOrderbookRowCollection> GetFleetOrderbookRows(string brand, bool includeHidden, FleetOrderbookSummaryRow filterChoices, DateTime? includeRemovedAfter, Model.DealerGroupName dealerGroup)
      {
         IEnumerable<FleetOrderbookRowVM> rows = await fleetOrderbookCache.GetFleetOrderbookRows(brand, includeHidden, includeRemovedAfter, dealerGroup);

         List<FleetOrderbookField> fields = GetFields(new FleetOrderbookRowVM());
         List<string> results = new List<string>();
         foreach (var item in rows)
         {
            string thisRowAsString = string.Empty;
            foreach (var field in fields)
            {
               object value = typeof(FleetOrderbookRowVM).GetProperty(field.FieldName).GetValue(item);
               if (field.FieldIsDate)
               {
                  thisRowAsString += (value != null ? StaticHelpersService.dateOnlyToString((DateTime)value) : string.Empty);
               }
               else
               {
                  thisRowAsString += (value != null ? value.ToString() : string.Empty);
               }
               thisRowAsString += "|^|";
            }
            results.Add(thisRowAsString);
         }

         return new FleetOrderbookRowCollection()
         {
            Fields = fields,
            RowsAsStrings = results
         };
      }


      public async Task<IEnumerable<FleetOrderbookSummaryRow>> GetFleetOrderbookSummaryRows(string brand)
      {
         return await fleetOrderbookCache.GetFleetOrderbookSummaryRows(userService.GetUserDealerGroupName(), brand);
      }


      private static List<FleetOrderbookField> GetFields(object classInstance)
      {
         List<FleetOrderbookField> fields = new List<FleetOrderbookField>();

         Type classType = classInstance.GetType();
         PropertyInfo[] properties = classType.GetProperties();

         foreach (PropertyInfo property in properties)
         {
            if (property.Name == "TrueAge")
            {
               { }
            }
            bool isDate = property.PropertyType == typeof(DateTime);
            bool isBool = property.PropertyType == typeof(bool);
            bool isNullableDate = property.PropertyType == typeof(DateTime?);
            List<string> numberTypes = new List<string>() { "Nullable`1", "Int32", "Decimal" };
            fields.Add(new FleetOrderbookField()
            {
               FieldName = property.Name,
               FieldIsBool = isBool,
               FieldIsDate = isDate || isNullableDate,
               FieldIsNumber = numberTypes.Contains(property.PropertyType.Name),
               FieldIsNullable = property.PropertyType.Name == "Nullable`1"
            });

         }

         return fields;

      }

      public async Task<int> AddNewComment(FleetOrderbookNewCommentParams parms, Model.DealerGroupName dealerGroup)
      {
         parms.UserId = userService.GetUserId();
         var res = await fleetOrderbookDataAccess.AddNewComment(parms, dealerGroup);
         fleetOrderbookCache.RefreshOrderbookRowCachesFireAndForget(dealerGroup);
         return res;
      }
      public async Task<IEnumerable<FleetOrderComment>> AddNewComments(FleetOrderbookNewCommentsParams parms)
      {
         parms.UserId = userService.GetUserId();
         var res = await fleetOrderbookDataAccess.AddNewComments(parms, userService.GetUserDealerGroupName());
         fleetOrderbookCache.RefreshOrderbookRowCachesFireAndForget(userService.GetUserDealerGroupName());
         return res;
      }
      public async Task UpdateComment(FleetOrderbookUpdatedCommentParams parms, Model.DealerGroupName dealerGroup)
      {
         parms.UserId = userService.GetUserId();
         await fleetOrderbookDataAccess.UpdateComment(parms, dealerGroup);
         fleetOrderbookCache.RefreshOrderbookRowCachesFireAndForget(dealerGroup);
      }

      public async Task DeleteComment(int commentId, Model.DealerGroupName dealerGroup)
      {
         await fleetOrderbookDataAccess.DeleteComment(commentId, userService.GetUserId(), dealerGroup);
         fleetOrderbookCache.RefreshOrderbookRowCachesFireAndForget(dealerGroup);
      }




      public async Task UpdateProperties(FleetOrderbookUpdatePropsParams parms, FleetOrderbookSummaryRow filterChoices, Model.DealerGroupName dealerGroup)
      {
         await fleetOrderbookDataAccess.UpdateProperties(parms, dealerGroup);
         fleetOrderbookCache.RefreshAllCachesFireAndForget(dealerGroup);
      }



      public async Task<IEnumerable<FleetOrderbookEditableOption>> GetEditableOptions(Model.DealerGroupName dealerGroup)
      {
         return await fleetOrderbookDataAccess.GetEditableOptions(dealerGroup);
      }






      //-------------------------
      // Table States
      //------------------------- 
      public async Task<FleetOrderTableState> SaveTableState(FleetOrderbookTableStateParams parms, Model.DealerGroupName dealerGroup)
      {
         return await fleetOrderbookDataAccess.SaveTableState(parms, userService.GetUserId(), dealerGroup);
      }


      public async Task DeleteTableState(string label, Model.DealerGroupName dealerGroup)
      {
         await fleetOrderbookDataAccess.DeleteTableState(label, userService.GetUserId(), dealerGroup);
         return;
      }


      public async Task<FleetOrderbookTableStateParams> GetTableState(string label, bool isRenault)
      {
         return await fleetOrderbookDataAccess.GetTableState(label, isRenault, userService.GetUserId(), userService.GetUserDealerGroupName());
      }

      public async Task<IEnumerable<string>> GetTableStateLabels(bool isRenault)
      {
         return await fleetOrderbookDataAccess.GetTableStateLabels(isRenault, userService.GetUserId(), userService.GetUserDealerGroupName());
      }


      public async Task<FleetOrderbookTableState> GetLastLoadedTableState(bool isRenault)
      {
         return await fleetOrderbookDataAccess.GetLastLoadedTableState(isRenault, userService.GetUserId(), userService.GetUserDealerGroupName());
      }




      //-------------------------
      // FONs
      //------------------------- 

      public async Task<IEnumerable<FonName>> GetFonNames()
      {
         return await fleetOrderbookDataAccess.GetFonNames(userService.GetUserDealerGroupName());
      }

      public async Task<FonName> UpdateFonName(FonName fonName)
      {
         return await fleetOrderbookDataAccess.UpdateFonName(fonName, userService.GetUserDealerGroupName());
      }

      public async Task<FonName> AddFonName(FonName fonName)
      {
         return await fleetOrderbookDataAccess.AddFonName(fonName, userService.GetUserDealerGroupName());
      }

      public async Task<FonName> DeleteFonName(FonName fonName)
      {
         return await fleetOrderbookDataAccess.DeleteFonName(fonName, userService.GetUserDealerGroupName());
      }


      public async Task<FileStreamResult> DownloadUploadTemplate(ExcelChoices prams, int userId)
      {
         var stream = await MakeWorkbook(prams);

         var res = new FileStreamResult(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

         return res;

      }


      public async Task<MemoryStream> MakeWorkbook(ExcelChoices prams)
      {
         MemoryStream stream = new MemoryStream();

         using (var package = new ExcelPackage(stream))
         {
            if (prams.SheetName.Contains("Renault"))
            {
               await MakeSheetRenault(prams, package);
            }
            else
            {
               await MakeSheetNissan(prams, package);
            }

            package.Save();
         }

         stream.Position = 0;
         return stream;
      }


      private async System.Threading.Tasks.Task MakeSheetRenault(ExcelChoices prams, ExcelPackage package)
      {

         var worksheet = package.Workbook.Worksheets.Add("Renault Upload");

         worksheet.Cells[1, 2].Value = "Fleet OrderBook Upload Template - Renault";
         worksheet.Cells[1, 2].Style.Font.Bold = true;
         worksheet.Cells[1, 2].Style.Font.Size = 14;

         worksheet.Cells[2, 2].Value = "Use this template to update the fleet orderbook in Spark";
         worksheet.Cells[3, 2].Value = "Spark will skip any lines that do not have a COF or Chassis.   If a line has both, it will use COF.";

         ExcelSheetValues values = new ExcelSheetValues();

         List<ExcelColumnDef> colDefs = new List<ExcelColumnDef>()
                {
                    new ExcelColumnDef()  { columnFormatBody = ColumnFormat.label, columnFormatFooter = ColumnFormat.label, shouldTotal = false, width = 8, header = "", tobeRemoved = false  },
                    new ExcelColumnDef()  { columnFormatBody = ColumnFormat.label, columnFormatFooter = ColumnFormat.label, shouldTotal = false, width = 13, header = "COF", tobeRemoved = false  },
                    new ExcelColumnDef()  { columnFormatBody = ColumnFormat.label, columnFormatFooter = ColumnFormat.label, shouldTotal = false, width = 27, header = "Chassis", tobeRemoved = false  },

                    new ExcelColumnDef()  { columnFormatBody = ColumnFormat.label, columnFormatFooter = ColumnFormat.label, shouldTotal = false, width = 30, header = "Stock Category", tobeRemoved = false  },
                    new ExcelColumnDef()  { columnFormatBody = ColumnFormat.label, columnFormatFooter = ColumnFormat.label, shouldTotal = false, width = 13, header = "Vehicle Reg", tobeRemoved = false  },
                    new ExcelColumnDef()  { columnFormatBody = ColumnFormat.label, columnFormatFooter = ColumnFormat.label, shouldTotal = false, width = 9, header = "Driver Pack Required", tobeRemoved = false  },
                    new ExcelColumnDef()  { columnFormatBody = ColumnFormat.label, columnFormatFooter = ColumnFormat.label, shouldTotal = false, width = 9, header = "Driver Pack Ordered", tobeRemoved = false  },
                    new ExcelColumnDef()  { columnFormatBody = ColumnFormat.label, columnFormatFooter = ColumnFormat.label, shouldTotal = false, width = 13, header = "Delivery Date", tobeRemoved = false  },

                    new ExcelColumnDef()  { columnFormatBody = ColumnFormat.label, columnFormatFooter = ColumnFormat.label, shouldTotal = false, width = 44, header = "Comment", tobeRemoved = false  },
                    new ExcelColumnDef()  { columnFormatBody = ColumnFormat.label, columnFormatFooter = ColumnFormat.label, shouldTotal = false, width = 31, header = "Account Handler Name", tobeRemoved = false  },

                    new ExcelColumnDef()  { columnFormatBody = ColumnFormat.date, columnFormatFooter = ColumnFormat.date, shouldTotal = false, width = 31, header = "Converter Build Date", tobeRemoved = false  },
                    new ExcelColumnDef()  { columnFormatBody = ColumnFormat.date, columnFormatFooter = ColumnFormat.date, shouldTotal = false, width = 31, header = "Converter Estimated Completion Date", tobeRemoved = false  },

                };

         int colCount = 1;

         foreach (ExcelColumnDef colDef in colDefs)
         {
            worksheet.Cells[5, colCount].Value = colDef.header;
            worksheet.Column(colCount).Width = colDef.width;
            worksheet.Cells[5, colCount].Style.WrapText = true;
            colCount++;
         }

         // Add example row
         worksheet.Cells[8, 1].Value = "example";
         worksheet.Cells[8, 2].Value = "29670";

         worksheet.Cells[8, 3].Value = "VF1RFK00X70706953";
         worksheet.Cells[8, 3].Value = "VF1RFK00X70706953";

         worksheet.Cells[6, 4].Value = "CAR / PERSONALISATION";
         worksheet.Cells[7, 4].Value = "CAR / PERSONALISATION";
         worksheet.Cells[8, 4].Value = "CAR / PERSONALISATION";

         worksheet.Cells[6, 6].Value = "n";
         worksheet.Cells[6, 7].Value = "n";

         worksheet.Cells[7, 6].Value = "n";
         worksheet.Cells[7, 7].Value = "n";

         worksheet.Cells[8, 6].Value = "y";
         worksheet.Cells[8, 7].Value = "n";

         worksheet.Cells[8, 8].Value = "27/09/2023";
         worksheet.Cells[8, 8].Style.Numberformat.Format = "dd/mm/yyyy";
         worksheet.Cells[8, 9].Value = "This is a comment";

         worksheet.Cells[9, 8].Value = "09/04/2024";
         worksheet.Cells[9, 8].Style.Numberformat.Format = "dd/mm/yyyy";
         worksheet.Cells[9, 9].Value = "1. This will set the Delivery Date to 09/04/2024";

         worksheet.Cells[10, 8].Value = "REMOVE";
         worksheet.Cells[10, 8].Style.Numberformat.Format = "dd/mm/yyyy";
         worksheet.Cells[10, 9].Value = "2. This will clear the Delivery Date for this order";

         worksheet.Cells[11, 8].Value = "";
         worksheet.Cells[11, 8].Style.Numberformat.Format = "dd/mm/yyyy";
         worksheet.Cells[11, 9].Value = "3. If blank, the Delivery Date will not be changed";

         worksheet.Cells[8, 10].Value = "John Johnson";

         worksheet.Cells[8, 11].Value = "27/11/2023";
         worksheet.Cells[8, 12].Style.Numberformat.Format = "dd/mm/yyyy";

         worksheet.Cells[8, 12].Value = "01/11/2023";
         worksheet.Cells[8, 12].Style.Numberformat.Format = "dd/mm/yyyy";

         for (int i = 1; i < 751; i++)
         {
            worksheet.Cells[i + 8, 1].Value = i;

            colCount = 1;

            foreach (ExcelColumnDef colDef in colDefs)
            {
               if (colDef.columnFormatBody == ColumnFormat.date)
               {
                  worksheet.Column(colCount).Style.Numberformat.Format = "dd/mm/yyyy";
               }

               colCount++;
            }

            worksheet.Cells[i + 8, 1].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Right;
         }

         values.colDefs = colDefs;

         // Colour the range
         // Define the range to fill
         var fillRange = worksheet.Cells["B9:L758"];

         // Set the background color
         fillRange.Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
         fillRange.Style.Fill.BackgroundColor.SetColor(System.Drawing.ColorTranslator.FromHtml("#FFFFCC"));

         // Apply borders to the range to maintain gridlines visibility
         fillRange.Style.Border.Top.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
         fillRange.Style.Border.Bottom.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
         fillRange.Style.Border.Left.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
         fillRange.Style.Border.Right.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
      }


      private async System.Threading.Tasks.Task MakeSheetNissan(ExcelChoices prams, ExcelPackage package)
      {

         var worksheet = package.Workbook.Worksheets.Add("Nissan Upload");

         worksheet.Cells[1, 2].Value = "Fleet OrderBook Upload Template - Nissan";
         worksheet.Cells[1, 2].Style.Font.Bold = true;
         worksheet.Cells[1, 2].Style.Font.Size = 14;

         worksheet.Cells[2, 2].Value = "Use this template to update the fleet orderbook in Spark";
         worksheet.Cells[3, 2].Value = "Spark will skip any lines that do not have a COF or Chassis.   If a line has both, it will use COF.";

         ExcelSheetValues values = new ExcelSheetValues();

         List<ExcelColumnDef> colDefs = new List<ExcelColumnDef>()
                {
                    new ExcelColumnDef()  { columnFormatBody = ColumnFormat.label, columnFormatFooter = ColumnFormat.label, shouldTotal = false, width = 8, header = "", tobeRemoved = false  },
                    new ExcelColumnDef()  { columnFormatBody = ColumnFormat.label, columnFormatFooter = ColumnFormat.label, shouldTotal = false, width = 13, header = "COF", tobeRemoved = false  },
                    new ExcelColumnDef()  { columnFormatBody = ColumnFormat.label, columnFormatFooter = ColumnFormat.label, shouldTotal = false, width = 27, header = "Chassis", tobeRemoved = false  },
                    new ExcelColumnDef()  { columnFormatBody = ColumnFormat.label, columnFormatFooter = ColumnFormat.label, shouldTotal = false, width = 27, header = "Veh Order No", tobeRemoved = false  },

                    new ExcelColumnDef()  { columnFormatBody = ColumnFormat.label, columnFormatFooter = ColumnFormat.label, shouldTotal = false, width = 30, header = "Stock Category", tobeRemoved = false  },
                    new ExcelColumnDef()  { columnFormatBody = ColumnFormat.label, columnFormatFooter = ColumnFormat.label, shouldTotal = false, width = 13, header = "Vehicle Reg", tobeRemoved = false  },
                    new ExcelColumnDef()  { columnFormatBody = ColumnFormat.label, columnFormatFooter = ColumnFormat.label, shouldTotal = false, width = 9, header = "Driver Pack Required", tobeRemoved = false  },
                    new ExcelColumnDef()  { columnFormatBody = ColumnFormat.label, columnFormatFooter = ColumnFormat.label, shouldTotal = false, width = 9, header = "Driver Pack Ordered", tobeRemoved = false  },
                    new ExcelColumnDef()  { columnFormatBody = ColumnFormat.label, columnFormatFooter = ColumnFormat.label, shouldTotal = false, width = 13, header = "Delivery Date", tobeRemoved = false  },

                    new ExcelColumnDef()  { columnFormatBody = ColumnFormat.label, columnFormatFooter = ColumnFormat.label, shouldTotal = false, width = 44, header = "Comment", tobeRemoved = false  },
                    new ExcelColumnDef()  { columnFormatBody = ColumnFormat.label, columnFormatFooter = ColumnFormat.label, shouldTotal = false, width = 31, header = "Account Handler Name", tobeRemoved = false  },

                    new ExcelColumnDef()  { columnFormatBody = ColumnFormat.date, columnFormatFooter = ColumnFormat.date, shouldTotal = false, width = 31, header = "Converter Build Date", tobeRemoved = false  },
                    new ExcelColumnDef()  { columnFormatBody = ColumnFormat.date, columnFormatFooter = ColumnFormat.date, shouldTotal = false, width = 31, header = "Converter Estimated Completion Date", tobeRemoved = false  },

                };

         int colCount = 1;

         foreach (ExcelColumnDef colDef in colDefs)
         {
            worksheet.Cells[5, colCount].Value = colDef.header;
            worksheet.Column(colCount).Width = colDef.width;
            worksheet.Cells[5, colCount].Style.WrapText = true;
            colCount++;
         }

         // Add example row
         worksheet.Cells[8, 1].Value = "example";
         worksheet.Cells[8, 2].Value = "29670";

         worksheet.Cells[8, 3].Value = "VF1RFK00X70706953";
         worksheet.Cells[8, 3].Value = "VF1RFK00X70706953";

         worksheet.Cells[6, 5].Value = "CAR / PERSONALISATION";
         worksheet.Cells[7, 5].Value = "CAR / PERSONALISATION";
         worksheet.Cells[8, 5].Value = "CAR / PERSONALISATION";

         worksheet.Cells[6, 7].Value = "n";
         worksheet.Cells[6, 8].Value = "n";

         worksheet.Cells[7, 7].Value = "n";
         worksheet.Cells[7, 8].Value = "n";

         worksheet.Cells[8, 7].Value = "y";
         worksheet.Cells[8, 8].Value = "n";

         worksheet.Cells[8, 9].Value = "27/09/2023";
         worksheet.Cells[8, 9].Style.Numberformat.Format = "dd/mm/yyyy";
         worksheet.Cells[8, 10].Value = "This is a comment";

         worksheet.Cells[9, 9].Value = "09/04/2024";
         worksheet.Cells[9, 9].Style.Numberformat.Format = "dd/mm/yyyy";
         worksheet.Cells[9, 10].Value = "1. This will set the Delivery Date to 09/04/2024";

         worksheet.Cells[10, 9].Value = "REMOVE";
         worksheet.Cells[10, 9].Style.Numberformat.Format = "dd/mm/yyyy";
         worksheet.Cells[10, 10].Value = "2. This will clear the Delivery Date for this order";

         worksheet.Cells[11, 9].Value = "";
         worksheet.Cells[11, 9].Style.Numberformat.Format = "dd/mm/yyyy";
         worksheet.Cells[11, 10].Value = "3. If blank, the Delivery Date will not be changed";

         worksheet.Cells[8, 11].Value = "John Johnson";

         worksheet.Cells[8, 12].Value = "27/11/2023";
         worksheet.Cells[8, 13].Style.Numberformat.Format = "dd/mm/yyyy";

         worksheet.Cells[8, 13].Value = "01/11/2023";
         worksheet.Cells[8, 13].Style.Numberformat.Format = "dd/mm/yyyy";

         for (int i = 1; i < 751; i++)
         {
            worksheet.Cells[i + 8, 1].Value = i;

            colCount = 1;

            foreach (ExcelColumnDef colDef in colDefs)
            {
               if (colDef.columnFormatBody == ColumnFormat.date)
               {
                  worksheet.Column(colCount).Style.Numberformat.Format = "dd/mm/yyyy";
               }

               colCount++;
            }

            worksheet.Cells[i + 8, 1].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Right;
         }

         values.colDefs = colDefs;

         // Colour the range
         // Define the range to fill
         var fillRange = worksheet.Cells["B9:M758"];

         // Set the background color
         fillRange.Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
         fillRange.Style.Fill.BackgroundColor.SetColor(System.Drawing.ColorTranslator.FromHtml("#FFFFCC"));

         // Apply borders to the range to maintain gridlines visibility
         fillRange.Style.Border.Top.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
         fillRange.Style.Border.Bottom.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
         fillRange.Style.Border.Left.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
         fillRange.Style.Border.Right.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
      }
   }

}