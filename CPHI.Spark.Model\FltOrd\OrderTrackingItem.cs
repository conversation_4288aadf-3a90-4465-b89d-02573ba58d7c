﻿
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CPHI.Spark.Model.FltOrd
{

   // Only manual input 
   public class OrderTrackingItem
   {



      [Key]
      public int Id { get; set; } //OK


      //-----------------------------------------------
      //FKs to the 'main' tables we load each day
      //-----------------------------------------------
      public int? NissanOrderItemId { get; set; }
      [ForeignKey("NissanOrderItemId")]
      public NissanOrderItem NissanOrderItem { get; set; }

      public int? RenaultOrderItemId { get; set; }
      [ForeignKey("RenaultOrderItemId")]
      public RenaultOrderItem RenaultOrderItem { get; set; }



      //-----------------------------------------------
      //FKs for items that User will pick from
      //-----------------------------------------------
      //public int? FonNameId { get; set; }    //perhaps we use both data and label.   e.g. 80305 Westwood Motors
      //[ForeignKey("FonNameId")]
      //public FonName FonName { get; set; }

      public int? StockCategoryItemId { get; set; }  //ok.   yes, only a couple.  user picks them.  e.g. CAR / PERSONALISATION or FULL CONVERSION
      [ForeignKey("StockCategoryItemId")]
      public StockCategoryItem StockCategoryItem { get; set; }

      public int? AlarmDataItemId { get; set; }  //ok.   yes, only a couple.  user picks them.  e.g. Alarm will be fitted , No Alarm Fitted - Future Build
      [ForeignKey("AlarmDataItemId")]
      public AlarmDataItem AlarmDataItem { get; set; }



      //-----------------------------------------------
      //user will update these dates
      //-----------------------------------------------
      public DateTime? FRD { get; set; }  //OK
      public DateTime? InvoiceDate { get; set; }  //OK
      public DateTime? DeliveryDate { get; set; }  //OK



      //-----------------------------------------------
      //User fills in free text.
      //-----------------------------------------------
      [MaxLength(50)]
      public string AgeingProfile { get; set; }  //OK.  Free Text
      [MaxLength(50)]
      public string Customer { get; set; }  //Free text
      [MaxLength(50)]
      public string CustomerOrderNo { get; set; }  //Nissan enters this
      [MaxLength(50)]
      public string DeliveryDepot { get; set; }  //Nissan enters this
      [MaxLength(50)]
      public string EndUser { get; set; }  //Free text
      [MaxLength(50)]
      public string PivgReference { get; set; }  //Free text
      [MaxLength(50)]
      public string SalesPerson { get; set; }  //Free text
      [MaxLength(50)]
      public string FonCode { get; set; }  //Free text
      [MaxLength(50)]
      public string Reg { get; set; }  //Free text


      //-----------------------------------------------
      //User fills in bool.
      //-----------------------------------------------
      public bool DriverPackRequired { get; set; }
      public bool DriverPackOrdered { get; set; }

      //-----------------------------------------------
      //user will pick to hide from view
      //-----------------------------------------------
      public bool IsHidden { get; set; }   //indicates user doesn't want to see this item
      public DateTime? RemovedDate { get; set; }   //indicates user has archived.   Per Chris he would describe this as 'is delivered'.   

      [MaxLength(50)]
      public string AccountHandlerName { get; set; }
      public DateTime? ConvBldDte { get; set; }
      public DateTime? ConvEstComplDte { get; set; }
      [MaxLength(50)]
      public string ConverterName { get; set; }
      public DateTime? ConverterBuildStartDate { get; set; }

      //SPK-4313 4 more fields
      [MaxLength(50)]
      public string RUKIsGoing { get; set; }
      [MaxLength(75)]
      public string RUKForecastReason { get; set; }
      [MaxLength(50)]
      public string RUKForecastSubReason { get; set; }
      public DateTime? RUKForecastFRD { get; set; }


      // New properties added - 1st Sept 25
      [MaxLength(100)]
      public string DeliveryAgent { get; set; }

   }






}
