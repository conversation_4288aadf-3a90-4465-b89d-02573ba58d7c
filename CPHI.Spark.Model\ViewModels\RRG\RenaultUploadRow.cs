﻿using System;

namespace CPHI.Spark.Model.ViewModels.RRG

{
   public class UploadRow
   {
      public int RowId { get; set; }
      public int COF { get; set; } // Renault only

      public string VehicleOrderNumber { get; set; } // Nissan only
      public string Chassis { get; set; }
      public string StockCategory { get; set; }
      public string Reg { get; set; }

      public bool? DriverPackRequired { get; set; }
      public bool? DriverPackOrdered { get; set; }
      public DateTime? DeliveryDate { get; set; }
      public string Comment { get; set; }


      public string EndUser { get; set; }
      public string SalesPerson { get; set; }
      public string DeliveryDepot { get; set; }
      public string FonCode { get; set; }


      public string AccountHandlerName { get; set; }
      public DateTime? ConvBldDte { get; set; }
      public DateTime? ConvEstComplDte { get; set; }
      public string ConverterName { get; set; }
      public DateTime? ConverterBuildStartDate { get; set; }



      // New properties added - 1st Sept 25
      public string PivgReference { get; set; }
      public string DeliveryAgent { get; set; }



      public bool ClearDeliveryDate { get; set; }
   }
}
