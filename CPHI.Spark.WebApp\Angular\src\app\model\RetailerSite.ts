import { StrategyVersionVM } from "./StrategyVersionVM";
import { TradePriceSetting } from "./TradePriceSetting";
import { SiteVM } from "./main.model";

export class RetailerSite {
   constructor(itemIn: RetailerSite) {

      this.Id = itemIn.Id;
      this.Site_Id = itemIn.Site_Id;
      this.DealerGroup_Id = itemIn.DealerGroup_Id;
      this.Name = itemIn.Name;
      this.RetailerId = itemIn.RetailerId;
      this.IsActive = itemIn.IsActive;
      this.Postcode = itemIn.Postcode;
      this.Makes = itemIn.Makes;
      this.FakeName = itemIn.FakeName;

      this.UpdatePricesAutomatically = itemIn.UpdatePricesAutomatically;
      this.UpdatePricesMon = itemIn.UpdatePricesMon;
      this.UpdatePricesTue = itemIn.UpdatePricesTue;
      this.UpdatePricesWed = itemIn.UpdatePricesWed;
      this.UpdatePricesThu = itemIn.UpdatePricesThu;
      this.UpdatePricesFri = itemIn.UpdatePricesFri;
      this.UpdatePricesPubHolidays = itemIn.UpdatePricesPubHolidays;
      this.UpdatePricesSat = itemIn.UpdatePricesSat;
      this.UpdatePricesSun = itemIn.UpdatePricesSun;
      this.WhenToActionChangesEachDay = itemIn.WhenToActionChangesEachDay;
      this.MaximumOptOutDays = itemIn.MaximumOptOutDays;
      this.MinimumAutoPriceDecrease = itemIn.MinimumAutoPriceDecrease;
      this.MinimumAutoPriceIncrease = itemIn.MinimumAutoPriceIncrease;
      this.MinimumAutoPricePercentDecrease = itemIn.MinimumAutoPricePercentDecrease;
      this.MinimumAutoPricePercentIncrease = itemIn.MinimumAutoPricePercentIncrease;
      this.LocalBargainThreshold = itemIn.LocalBargainThreshold;
      this.LocationMovePoundPerMile = itemIn.LocationMovePoundPerMile;
      this.LocationMoveFixedCostPerMove = itemIn.LocationMoveFixedCostPerMove;

      // Valuation settings
      this.TargetMargin = itemIn.TargetMargin;
      this.TargetAdditionalMech = itemIn.TargetAdditionalMech;
      this.TargetPaintPrep = itemIn.TargetPaintPrep;
      this.TargetAuctionFee = itemIn.TargetAuctionFee;
      this.TargetDelivery = itemIn.TargetDelivery;
      this.TargetWarrantyFee = itemIn.TargetWarrantyFee;
      this.TargetOtherCost = itemIn.TargetOtherCost;

      // Leaving Vehicles settings
      this.LeavingVehicles_IncludeUnPublished = itemIn.LeavingVehicles_IncludeUnPublished;
      this.TrackLeavingVehicles = itemIn.TrackLeavingVehicles;
      this.AdminFee = itemIn.AdminFee;

      this.CompetitorPlateRange = itemIn.CompetitorPlateRange;
      this.CompetitorSearchRange = itemIn.CompetitorSearchRange;
   }



   Id: number;

    //FKs
   Site_Id: number;
   DealerGroup_Id: number;
   Name: string;
   RetailerId: number;
   IsActive: boolean;
   Postcode: string;
   Makes: string;
   FakeName: string;
   //Updating Prices
   UpdatePricesAutomatically: boolean;
   UpdatePricesMon: boolean;
   UpdatePricesTue: boolean;
   UpdatePricesWed: boolean;
   UpdatePricesThu: boolean;
   UpdatePricesFri: boolean;
   UpdatePricesPubHolidays: boolean;
   UpdatePricesSat: boolean;
   UpdatePricesSun: boolean;
   WhenToActionChangesEachDay: number;
   MaximumOptOutDays: number;
   MinimumAutoPriceDecrease: number;
   MinimumAutoPriceIncrease: number;
   MinimumAutoPricePercentDecrease: number;
   MinimumAutoPricePercentIncrease: number;

   //Local Bargains
   LocalBargainThreshold: number;

   //Location Optimiser
   LocationMovePoundPerMile: number;
   LocationMoveFixedCostPerMove: number;

   DaysMeasure: string; //DaysListed for most.  Pentagon like DaysInStock

   //valuation
   TargetMargin: number;
   TargetAdditionalMech: number;
   TargetPaintPrep: number;
   TargetAuctionFee: number;
   TargetDelivery: number;
   TargetWarrantyFee: number;
   TargetOtherCost: number;

   CompetitorPlateRange:number;
   CompetitorSearchRange:number;

   //Properties for reports
   DefaultVehicleTypes: string; //array of the default chosen vehicle types
   VehicleTypes: string;  //array of all the possible vehicle types (vehicle type from DMS records)
   MorningReportDISUpTo: number;   //limits vehicles based on days in stock.  Pentagon requested this.
   LifecycleStatusDefaults: string;  //e.g. FORECOURT, SALE_IN_PROGRESS etc.
   IncludeUnPublishedAds: boolean;
   //ShowUnpublishedAds: boolean;
   ShowNewVehicles: boolean;
   AllowPriceScenarios: boolean; //the v12 thing
   SeparateBuyingStrategy: boolean;
   SeparateBuyingStrategy2: boolean;
   AllowTestStrategy: boolean;
   StockReport_ShowDmsSellingPrice: boolean;   //Vindis requested this
   StockReport_ShowVsDmsSellingPrice: boolean;  //Vindis requested this
   StockReport_ShowPhysicalLocation: boolean; //Vindis requested this
//   DefaultToDaysInStock: boolean; // //NOT TO BE USED, instead we now have ShowDaysInStockByDefault in GlobalParam. determines the col that appears by default on the main report

   // Leaving Vehicles settings
   LeavingVehicles_IncludeUnPublished?: boolean;
   TrackLeavingVehicles?: boolean;

   TradePriceSetting?: TradePriceSetting;
   AdminFee:number;
}


