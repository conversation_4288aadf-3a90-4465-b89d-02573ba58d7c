﻿using CPHI.Spark.DataAccess;
using CPHI.Spark.DataAccess.AutoPrice;
using CPHI.Spark.Model;
using CPHI.Spark.Model.Services;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using log4net;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;

namespace CPHI.Spark.BusinessLogic.AutoPrice
{
   public class DaysToSellService
   {
      public readonly string _connString;
      public readonly AutoTraderConfig _atConfig;
      public readonly HttpClient httpClient;
      private readonly IHttpClientFactory _httpClientFactory;
      public DaysToSellService(string connString, AutoTraderConfig atConfig, IHttpClientFactory httpClientFactory)
      {
         this._connString = connString;
         this._atConfig = atConfig;
         this.httpClient = httpClientFactory.CreateClient();
         this._httpClientFactory = httpClientFactory;

      }

      public async Task<List<DaysToSellRequest>> FindDaysToSell(List<DaysToSellRequest> items, ILog logger)
      {
         //HttpClient httpClient = new HttpClient();
         AutoTraderVehicleMetricsClient atMetricsClient = new AutoTraderVehicleMetricsClient(
            _httpClientFactory, _atConfig.AutotraderApiKey,
          _atConfig.AutotraderApiSecret, _atConfig.AutotraderBaseURL);
         var atTokenClient = new AutoTraderApiTokenClient(_httpClientFactory, _atConfig.AutotraderApiKey,
          _atConfig.AutotraderApiSecret, _atConfig.AutotraderBaseURL);

         var bearerToken = (await atTokenClient.GetToken());
         ConcurrentBag<DaysToSellRequest> updatedItems = new ConcurrentBag<DaysToSellRequest>();

         // Make a list for all the work to do.   We effectively line up ALL the tasks at once
         List<Func<Task>> allWork = new List<Func<Task>>();
         foreach (var item in items)
         {
            var workItem = PrepareExecuteFindDaysToSell(
                bearerToken,
                item,
                atMetricsClient,
                updatedItems,
                logger
            );

            // "workItem" is a Func<Task> that *encapsulates* the async call
            allWork.Add(workItem);
         }

         // Start tasks simultaneously
         var tasks = allWork.Select(async workItem =>
         {
            await workItem();
         });

         await Task.WhenAll(tasks);

         return updatedItems.ToList();
      }

     

      private Func<Task> PrepareExecuteFindDaysToSell(TokenResponse bearerToken, DaysToSellRequest item,
       AutoTraderVehicleMetricsClient atMetricsClient, ConcurrentBag<DaysToSellRequest> updatedItems,
        ILog logger)
      {
         return async () =>
         {
            try
            {
               if (item.ValuationMktAvRetail == null) return;
               if (item.DerivativeId == null) return;

               // Basic boundary checks:
               if (item.AdvertisedPrice > 1.1M * item.ValuationMktAvRetail
                      || item.AdvertisedPrice < 0.9M * item.ValuationMktAvRetail)
               {
                  // It's better to set to zero (which we later filter out) than give a misleading value 
                  item.DaysToSellAtCurrentSelling = 0;
                  updatedItems.Add(item);
                  return;
               }

               if (item.AdvertisedPrice > 1.1M * item.ValuationAdjRetail
                      || item.AdvertisedPrice < 0.9M * item.ValuationAdjRetail)
               {
                  // It's better to set to zero (which we later filter out) than give a misleading value 
                  item.DaysToSellAtCurrentSelling = 0;
                  updatedItems.Add(item);
                  return;
               }

               // Prepare parameters
               DateTime adFirstRegdDate;
               int mileageToUse;
               DateTime dateOnForecourt = item.DateOnForecourt ?? DateTime.Now.Date;

               ConstantMethodsService.GetFirstRegisteredDateAndMileage(
                      item.FirstRegisteredDate,
                      dateOnForecourt,
                      item.OdometerReading,
                      out adFirstRegdDate,
                      out mileageToUse);

               var parms = new GetAdvertPriceAdjustedDaysToSellParams
               {
                  AutotraderBaseURL = _atConfig.AutotraderBaseURL,
                  AdvertiserId = item.RetailerSiteRetailerId,
                  DerivativeId = item.DerivativeId,
                  FirstRegistrationDate = adFirstRegdDate,
                  OdometerReadingMiles = mileageToUse,
                  Amount = item.AdvertisedPrice,
                  UseSpecificOptions = item.VehicleHasOptionsSpecified,
                  SpecificOptionNames = item.VehicleHasOptionsSpecified
                                                 ? item.PortalOptions.Split(',')
                                                 : new List<string>(),
                  AverageValuation = item.ValuationMktAvRetail ?? 0,
                  AdjustedValuation = item.ValuationAdjRetail ?? 0
               };

               // Call the API
               var result = await atMetricsClient.GetAdvertPriceAdjustedDaysToSell(
                      parms, bearerToken.AccessToken, logger);

               if (result != 0)
               {
                  item.DaysToSellAtCurrentSelling = Math.Ceiling(result);
                  updatedItems.Add(item);
               }
            }
            catch (Exception ex)
            {
               logger.Error($"Failed on Snapshot Id #{item.SnapshotId}, {ex.Message}");
            }
         };
      }

   }
}