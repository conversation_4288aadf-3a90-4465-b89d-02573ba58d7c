export interface AutoPrice {
  defaultShowUnpublishedAds: boolean;
  defaultShowNewVehicles: boolean;
  //vehicleValuationShowCostingDetail: boolean
  stockReport: AutoPriceStockReport;
  applyPriceScenarios: boolean;
  //allowChooseNewStrategy: boolean;
  separateBuyingStrategy: boolean;
  separateBuyingStrategy2: boolean;
  lifecycleStatusDefault: string[];
  allowTestStrategy: boolean;
  vehicleTypes: string[];
  defaultVehicleTypes: string[];

}

export interface AutoPriceStockReport {
  showDMSSellingPrice_Col: boolean;
  showVsDMSSellingPrice_Col: boolean;
  showPhysicalLocation_Col: boolean,
}