
--UnifiedDB - Updated
CREATE OR ALTER PROCEDURE [dbo].[GET_FleetOrderbookRowsRenault]
(
    @includeHidden bit,
    @includeRemovedAfter Date,
    @rowIds NVARCHAR(MAX) = NULL,
    @dealerGroupId INT
)
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @todaysDate DateTime = GETDATE();
    DECLARE @endOfMonth DateTime = EOMONTH(@todaysDate);

    -----------------------------------
    -- Populate rowIds that we want
    -----------------------------------
    DECLARE @rowIdsTable TABLE (Id INT);
    INSERT INTO @rowIdsTable (Id)
    SELECT CAST(Value AS INT) 
    FROM STRING_SPLIT(ISNULL(@rowIds, ''), ',');

    -----------------------------------
    -- Main Query
    -----------------------------------
    SELECT DISTINCT 
        r.Id AS RenaultOrNissanOrderItemId,
        r.CustomerOrderNo,
        r.Chassis,
        r.VehicleOrderNumber,
        r.OwningMainDealer,
        r.Customer,
        ss.Status AS Status,
        sci.StockCategory AS StockCategory,
        r.Model,
        r.Version,
        r.Colour,
        r.Options,
        r.SalesExecPin,
        r.LabelOptions,
        sc.Description AS SalesChannel,
        r.DeliveryAccount AS DropCode,
        r.OrderComments AS Comments,
        r.CustomerOrdCreationDate AS OrderDate,
        r.ActualMatchedDate,
        cosi.CustomerOrderStatus AS CustOrdStatus,
        r.PromAtic,
        r.ProbableAtic,
        r.ActualAtic,
        r.DddDate AS DDDDate,
        r.RegistrationDate AS RegistrationDate,
        ct.Type AS CustomerType,
        DATEDIFF(day, r.ActualAtic, @todaysDate) AS TrueAge,
        r.InvoiceDate AS InvoiceDateFromRenault,
        CASE 
            WHEN o.Reg IS NULL OR o.Reg = '' THEN r.RegistrationNo
            ELSE o.Reg 
        END AS Reg, -- If there is a Reg on the item, use this - else use Report Reg. If neither, this will be CDK Reg (assigned via constructor)
        r.RegistrationNo,
        r.Frd AS FRD,
        r.DeliveryAccount AS DeliveryAccount,
        r.FonCode AS FonCode,
        fon.Description AS FonDescription,
        bs.Status AS BcaStatus,
        b.BcaArrivalDate AS BcaArrivalDate,
        b.BcaDdd AS BcaDdd,
        b.BcaEstArrivalDate AS BcaEstArrivalDate,
        b.Campaign,
        b.Damage,
        b.Estimate,
        b.Mechanical,
        'Renault' AS Brand,
        DATEDIFF(day, r.InvoiceDate, @endOfMonth) AS AgeAtEom,
        ISNULL(o.DeliveryDate, NULL) AS DeliveryDate,
        r.Comment,
        r.OrderComments,
        o.IsHidden,
        o.RemovedDate AS OrderMarkedRemovedDate,
        r.IsRemoved,
        eoi.OrderNo AS LeaseNumber,
        eoi.Stage AS EbbonStage,
        esi.Status AS EbbonStatus,
        o.DriverPackOrdered,
        o.DriverPackRequired,
        r.PlyOrderDate,
        r.PlyFitmentDate,
        r.PlyComplDate,
        r.PlyAccName,
        r.PlyDealerCost,
        o.AccountHandlerName,
        o.DeliveryAgent,
        o.PivgReference,
        o.ConvBldDte,
        o.ConvEstComplDte,
        o.ConverterName,
        o.ConverterBuildStartDate,
        o.RUKIsGoing,
        o.RUKForecastReason,
        o.RUKForecastSubReason,
        o.RUKForecastFRD,
		Ids.Id
    INTO #preDealsTableSet1
    FROM fltord.RenaultOrderItems r
    INNER JOIN fltord.StockStatusItems ss ON ss.Id = r.StockStatusItemId
    INNER JOIN RegChannels sc ON sc.Id = r.SalesChannelId
    INNER JOIN fltord.CustomerTypes ct ON ct.Id = r.CustomerTypeId
    LEFT JOIN @rowIdsTable ids ON ids.Id = r.Id
    LEFT JOIN fltord.CustomerOrderStatusItems cosi ON cosi.Id = r.CustomerOrderStatusItemId
    LEFT JOIN fltord.FonNames fon ON fon.Code = r.FonCode
    LEFT JOIN fltord.BcaStockItems b ON b.Id = r.BcaStockItemId AND b.IsRemoved = 0
    LEFT JOIN fltord.BcaStatusItems bs ON b.BcaStatusId = bs.Id
    LEFT JOIN fltord.OrderTrackingItems o ON o.RenaultOrderItemId = r.Id
    LEFT JOIN fltord.StockCategoryItems sci ON sci.Id = o.StockCategoryItemId
    LEFT JOIN fltord.EbbonOrderItems eoi ON eoi.Id = r.EbbonOrderItemid
    LEFT JOIN fltord.EbbonStatusItems esi ON esi.Id = eoi.EbbonStatusItemId
    WHERE r.DealerGroup_Id = @dealerGroupId
    
	--Splitted in two steps to improve performance
	SELECT * 
	INTO #preDealsTable
	FROM #preDealsTableSet1
	WHERE (@rowIds IS NULL OR Id IS NOT NULL)
    AND (IsHidden = 0 OR IsHidden IS NULL OR @includeHidden = 1)
    AND (OrderMarkedRemovedDate IS NULL OR OrderMarkedRemovedDate >= @includeRemovedAfter);

    -----------------------------------
    -- Get comments for these deals
    -----------------------------------
    SELECT 
        foc.Id,
        Date,
        Text,
        person.Name,
        RenaultOrderItem_Id
    INTO #CommentsOrderedByDate
    FROM fltord.FleetOrderComments foc
    INNER JOIN #preDealsTable pre ON pre.RenaultOrNissanOrderItemId = RenaultOrderItem_Id
    INNER JOIN People person ON person.Id = foc.PersonId
    WHERE foc.IsRemoved = 0
    AND person.DealerGroup_Id = @dealerGroupId
    ORDER BY foc.Date;

    -----------------------------------
    -- Get info from stock table
    -----------------------------------
    SELECT 
        CONCAT(StockNumber, '/', StockNumberSuffix) AS StockNumber,
        st.Chassis,
        st.StockDate,
        ROW_NUMBER() OVER (PARTITION BY StockNumber, StockNumberSuffix ORDER BY st.Id DESC) AS RowNumber
    INTO #stockdata
    FROM Stocks st
    INNER JOIN Sites s ON s.Id = st.Site_Id
    INNER JOIN #preDealsTable pre ON pre.Chassis = st.Chassis
    INNER JOIN VehicleTypes vt ON vt.id = st.VehicleType_Id
    WHERE StockNumber <> '' AND StockNumber IS NOT NULL AND st.Chassis <> ''
    AND s.DealerGroup_Id = @dealerGroupId
    AND vt.SuperType = 'New';

    -----------------------------------
    -- Get info from orders to vins
    -----------------------------------
    SELECT 
        CONCAT(VehicleNumber, '/', Suffix) AS StockNumber,
        vntv.Chassis,
        ROW_NUMBER() OVER (PARTITION BY vntv.Chassis ORDER BY vntv.Id DESC) AS RowNumber
    INTO #stocknumberFromOrders
    FROM VehicleNumbersToVins vntv
    INNER JOIN #preDealsTable pre ON pre.Chassis = vntv.Chassis
    WHERE VehicleNumber <> '' AND VehicleNumber IS NOT NULL AND vntv.Chassis <> ''
    AND vntv.DealerGroup_Id = @dealerGroupId;

    -----------------------------------
    -- Final return query
    -----------------------------------
    SELECT
        pdt.RenaultOrNissanOrderItemId,
        FORMAT(pdt.CustomerOrderNo, '000000') AS CustomerOrderNo,
        pdt.Chassis,
        pdt.VehicleOrderNumber,
        pdt.OwningMainDealer,
        UPPER(pdt.Customer) AS Customer,
        pdt.Status,
        pdt.StockCategory,
        pdt.Model,
        pdt.Version,
        pdt.Colour,
        pdt.SalesExecPin,
        pdt.Options,
        pdt.LabelOptions,
        pdt.SalesChannel,
        pdt.DropCode,
        pdt.Comments,
        pdt.EbbonStatus,
        CONVERT(date, pdt.OrderDate) AS OrderDate,
        pdt.ActualMatchedDate,
        pdt.CustOrdStatus,
        CONVERT(date, pdt.PromAtic) AS PromAtic,
        pdt.ProbableAtic,
        pdt.ActualAtic,
        pdt.DDDDate,
        pdt.RegistrationDate,
        pdt.CustomerType,
        pdt.TrueAge,
        CONVERT(date, pdt.FRD) AS FRD,
        pdt.DeliveryAccount,
        pdt.FonCode,
        UPPER(pdt.FonDescription) AS FonDescription,
        pdt.BcaStatus,
        pdt.BcaArrivalDate,
        pdt.BcaDdd,
        pdt.BcaEstArrivalDate,
        pdt.Campaign,
        pdt.Damage,
        pdt.Estimate,
        pdt.Mechanical,
        pdt.Brand,
        pdt.InvoiceDateFromRenault AS RenaultInvoiceDate,
        pdt.AgeAtEom,
        CONVERT(date, pdt.DeliveryDate) AS DeliveryDate,
        FORMAT(pdt.DeliveryDate, 'MMM yy') AS DeliveryMonth,
        pdt.Comment,
        pdt.OrderComments,
        pdt.IsHidden,
        pdt.OrderMarkedRemovedDate AS RemovedDate,
        pdt.EbbonStage,
        pdt.LeaseNumber,
        IIF(
            COUNT(com.Id) = 0,
            '',
            STRING_AGG(
                CONCAT(com.Id, '|', com.Date, '|', com.Text, '|', com.Name),
                '^#^'
            ) WITHIN GROUP (ORDER BY com.Date ASC)
        ) AS SparkComments,
        ISNULL(snfo.StockNumber, sd.StockNumber) AS 'StockNumber',
        IIF(pdt.InvoiceDateFromRenault IS NOT NULL, DATEDIFF(day, pdt.InvoiceDateFromRenault, @todaysDate), DATEDIFF(day, sd.StockDate, @todaysDate)) AS GFCStockAge,
        IIF(pdt.InvoiceDateFromRenault IS NOT NULL, 
            CASE 
                WHEN pdt.StockCategory = 'FULL CONVERSION' AND DATEDIFF(day, pdt.InvoiceDateFromRenault, @endOfMonth) > 90 THEN '90+'
                WHEN pdt.StockCategory = 'FULL CONVERSION' AND DATEDIFF(day, pdt.InvoiceDateFromRenault, @endOfMonth) > 60 THEN '60-90'
                WHEN pdt.StockCategory = 'FULL CONVERSION' AND DATEDIFF(day, pdt.InvoiceDateFromRenault, @endOfMonth) > 30 THEN '30-60'
                WHEN pdt.StockCategory = 'FULL CONVERSION' AND DATEDIFF(day, pdt.InvoiceDateFromRenault, @endOfMonth) > 0 THEN '0-30'
                ELSE 'Not Full Conversion'
            END,
            CASE 
                WHEN pdt.StockCategory = 'FULL CONVERSION' AND DATEDIFF(day, sd.StockDate, @endOfMonth) > 90 THEN '90+'
                WHEN pdt.StockCategory = 'FULL CONVERSION' AND DATEDIFF(day, sd.StockDate, @endOfMonth) > 60 THEN '60-90'
                WHEN pdt.StockCategory = 'FULL CONVERSION' AND DATEDIFF(day, sd.StockDate, @endOfMonth) > 30 THEN '30-60'
                WHEN pdt.StockCategory = 'FULL CONVERSION' AND DATEDIFF(day, sd.StockDate, @endOfMonth) > 0 THEN '0-30'
                WHEN sd.StockDate IS NULL THEN 'No Invoice Date'
                ELSE 'Not Full Conversion'
            END
        ) AS FullConversionAgeEoM,
        IIF(pdt.InvoiceDateFromRenault IS NOT NULL, 
            CASE 
                WHEN pdt.StockCategory = 'CAR / PERSONALISATION' AND DATEDIFF(day, pdt.InvoiceDateFromRenault, @endOfMonth) > 90 THEN '90+'
                WHEN pdt.StockCategory = 'CAR / PERSONALISATION' AND DATEDIFF(day, pdt.InvoiceDateFromRenault, @endOfMonth) > 60 THEN '60-90'
                WHEN pdt.StockCategory = 'CAR / PERSONALISATION' AND DATEDIFF(day, pdt.InvoiceDateFromRenault, @endOfMonth) > 30 THEN '30-60'
                WHEN pdt.StockCategory = 'CAR / PERSONALISATION' AND DATEDIFF(day, pdt.InvoiceDateFromRenault, @endOfMonth) > 0 THEN '0-30'
                ELSE 'Not Car Personalisation'
            END,
            CASE 
                WHEN pdt.StockCategory = 'CAR / PERSONALISATION' AND DATEDIFF(day, sd.StockDate, @endOfMonth) > 90 THEN '90+'
                WHEN pdt.StockCategory = 'CAR / PERSONALISATION' AND DATEDIFF(day, sd.StockDate, @endOfMonth) > 60 THEN '60-90'
                WHEN pdt.StockCategory = 'CAR / PERSONALISATION' AND DATEDIFF(day, sd.StockDate, @endOfMonth) > 30 THEN '30-60'
                WHEN pdt.StockCategory = 'CAR / PERSONALISATION' AND DATEDIFF(day, sd.StockDate, @endOfMonth) > 0 THEN '0-30'
                WHEN sd.StockDate IS NULL THEN 'No Invoice Date'
                ELSE 'Not Car Personalisation'
            END
        ) AS CarPersonalisationAgeEoM,
        COALESCE(MAX(d.TotalNLProfit), MAX(dealsOnReg.TotalNLProfit)) AS TotalNLProfit,
        COALESCE(MAX(d.InvoiceDate), MAX(dealsOnReg.InvoiceDate)) AS InvoiceDateToCustomer,
        NULLIF(TRIM(pdt.Reg), '') AS Reg,
        MAX(pdt.RegistrationNo) AS ReportReg,
        COALESCE(MAX(d.Reg),MAX(dealsOnReg.Reg)) AS DealsReg,
        IIF(pdt.IsRemoved = 1, 0, 1) AS IsOnLatestDownload,
        pdt.DriverPackOrdered,
        pdt.DriverPackRequired,
        pdt.PlyOrderDate,
        pdt.PlyFitmentDate,
        pdt.PlyComplDate,
        pdt.PlyAccName,
        pdt.PlyDealerCost,
        pdt.AccountHandlerName,
        pdt.DeliveryAgent,
        pdt.PivgReference,
        CONVERT(date, pdt.ConvBldDte) AS ConvBldDte,
        pdt.ConvEstComplDte,
        pdt.ConverterName,
        pdt.ConverterBuildStartDate AS ConvBldStartDte,
        pdt.RUKIsGoing,
        pdt.RUKForecastReason,
        pdt.RUKForecastSubReason,
        pdt.RUKForecastFRD
    FROM #preDealsTable pdt
    LEFT JOIN #stockdata sd ON sd.Chassis = pdt.Chassis AND sd.RowNumber = 1
    LEFT JOIN #stocknumberFromOrders snfo ON snfo.Chassis = pdt.Chassis AND snfo.RowNumber = 1
    LEFT JOIN DealLatests d ON d.StockNumber = ISNULL(snfo.StockNumber, sd.StockNumber) AND d.Units > 0 AND d.IsLateCost = 0
    LEFT JOIN DealLatests dealsOnReg ON dealsOnReg.Units = 1 AND dealsOnReg.Reg IS NOT NULL AND dealsOnReg.Reg <> '' AND dealsOnReg.Reg = pdt.RegistrationNo
    LEFT JOIN #CommentsOrderedByDate com ON com.RenaultOrderItem_Id = pdt.RenaultOrNissanOrderItemId
    GROUP BY 
        pdt.InvoiceDateFromRenault,
        pdt.RenaultOrNissanOrderItemId,
        FORMAT(pdt.CustomerOrderNo, '000000'),
        pdt.Chassis,
        pdt.VehicleOrderNumber,
        pdt.OwningMainDealer,
        UPPER(pdt.Customer),
        pdt.Status,
        pdt.StockCategory,
        pdt.Model,
        pdt.Version,
        pdt.Colour,
        pdt.SalesExecPin,
        pdt.Options,
        pdt.LabelOptions,
        pdt.SalesChannel,
        pdt.DropCode,
        pdt.Comments,
        pdt.EbbonStatus,
        pdt.OrderDate,
        pdt.ActualMatchedDate,
        pdt.CustOrdStatus,
        pdt.PromAtic,
        pdt.ProbableAtic,
        pdt.ActualAtic,
        pdt.DDDDate,
        pdt.RegistrationDate,
        pdt.CustomerType,
        pdt.TrueAge,
        pdt.FRD,
        pdt.DeliveryAccount,
        pdt.FonCode,
        UPPER(pdt.FonDescription),
        pdt.BcaStatus,
        pdt.BcaArrivalDate,
        pdt.BcaDdd,
        pdt.BcaEstArrivalDate,
        pdt.Campaign,
        pdt.Damage,
        pdt.Estimate,
        pdt.Mechanical,
        pdt.Brand,
        pdt.AgeAtEom,
        pdt.Reg,
        pdt.DeliveryDate,
        pdt.Comment,
        pdt.OrderComments,
        pdt.IsHidden,
        ISNULL(snfo.StockNumber, sd.StockNumber),
        IIF(pdt.InvoiceDateFromRenault IS NOT NULL, DATEDIFF(day, pdt.InvoiceDateFromRenault, @todaysDate), DATEDIFF(day, sd.StockDate, @todaysDate)),
        IIF(pdt.InvoiceDateFromRenault IS NOT NULL,
            CASE 
                WHEN pdt.StockCategory = 'FULL CONVERSION' AND DATEDIFF(day, pdt.InvoiceDateFromRenault, @endOfMonth) > 90 THEN '90+'
                WHEN pdt.StockCategory = 'FULL CONVERSION' AND DATEDIFF(day, pdt.InvoiceDateFromRenault, @endOfMonth) > 60 THEN '60-90'
                WHEN pdt.StockCategory = 'FULL CONVERSION' AND DATEDIFF(day, pdt.InvoiceDateFromRenault, @endOfMonth) > 30 THEN '30-60'
                WHEN pdt.StockCategory = 'FULL CONVERSION' AND DATEDIFF(day, pdt.InvoiceDateFromRenault, @endOfMonth) > 0 THEN '0-30'
                ELSE 'Not Full Conversion'
            END,
            CASE 
                WHEN pdt.StockCategory = 'FULL CONVERSION' AND DATEDIFF(day, sd.StockDate, @endOfMonth) > 90 THEN '90+'
                WHEN pdt.StockCategory = 'FULL CONVERSION' AND DATEDIFF(day, sd.StockDate, @endOfMonth) > 60 THEN '60-90'
                WHEN pdt.StockCategory = 'FULL CONVERSION' AND DATEDIFF(day, sd.StockDate, @endOfMonth) > 30 THEN '30-60'
                WHEN pdt.StockCategory = 'FULL CONVERSION' AND DATEDIFF(day, sd.StockDate, @endOfMonth) > 0 THEN '0-30'
                WHEN sd.StockDate IS NULL THEN 'No Invoice Date'
                ELSE 'Not Full Conversion'
            END
        ),
        IIF(pdt.InvoiceDateFromRenault IS NOT NULL,
            CASE 
                WHEN pdt.StockCategory = 'CAR / PERSONALISATION' AND DATEDIFF(day, pdt.InvoiceDateFromRenault, @endOfMonth) > 90 THEN '90+'
                WHEN pdt.StockCategory = 'CAR / PERSONALISATION' AND DATEDIFF(day, pdt.InvoiceDateFromRenault, @endOfMonth) > 60 THEN '60-90'
                WHEN pdt.StockCategory = 'CAR / PERSONALISATION' AND DATEDIFF(day, pdt.InvoiceDateFromRenault, @endOfMonth) > 30 THEN '30-60'
                WHEN pdt.StockCategory = 'CAR / PERSONALISATION' AND DATEDIFF(day, pdt.InvoiceDateFromRenault, @endOfMonth) > 0 THEN '0-30'
                ELSE 'Not Car Personalisation'
            END,
            CASE 
                WHEN pdt.StockCategory = 'CAR / PERSONALISATION' AND DATEDIFF(day, sd.StockDate, @endOfMonth) > 90 THEN '90+'
                WHEN pdt.StockCategory = 'CAR / PERSONALISATION' AND DATEDIFF(day, sd.StockDate, @endOfMonth) > 60 THEN '60-90'
                WHEN pdt.StockCategory = 'CAR / PERSONALISATION' AND DATEDIFF(day, sd.StockDate, @endOfMonth) > 30 THEN '30-60'
                WHEN pdt.StockCategory = 'CAR / PERSONALISATION' AND DATEDIFF(day, sd.StockDate, @endOfMonth) > 0 THEN '0-30'
                WHEN sd.StockDate IS NULL THEN 'No Invoice Date'
                ELSE 'Not Car Personalisation'
            END
        ),
        pdt.OrderMarkedRemovedDate,
        pdt.IsRemoved,
        pdt.EbbonStage,
        pdt.LeaseNumber,
        pdt.DriverPackOrdered,
        pdt.DriverPackRequired,
        pdt.PlyOrderDate,
        pdt.PlyFitmentDate,
        pdt.PlyComplDate,
        pdt.PlyAccName,
        pdt.PlyDealerCost,
        pdt.AccountHandlerName,
        pdt.DeliveryAgent,
        pdt.PivgReference,
        pdt.ConvBldDte,
        pdt.ConvEstComplDte,
        pdt.ConverterName,
        pdt.ConverterBuildStartDate,
        pdt.RUKIsGoing,
        pdt.RUKForecastReason,
        pdt.RUKForecastSubReason,
        pdt.RUKForecastFRD;

    DROP TABLE #preDealsTable;
    DROP TABLE #stockdata;
    DROP TABLE #stocknumberFromOrders;
    DROP TABLE #CommentsOrderedByDate;


END;

GO
