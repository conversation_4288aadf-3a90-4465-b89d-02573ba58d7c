using CPHI.Spark.Model;
using CPHI.Spark.Model.ViewModels;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace CPHI.Spark.Services
{
   public interface IGridFormatService
   {
      Task<List<CPHColDefDTO>> GetColumnDefinitions(string gridType, DealerGroupName dealerGroup, GetColumnDefinitionOptions options);
   }

   public class GridFormatService : IGridFormatService
   {
      public async Task<List<CPHColDefDTO>> GetColumnDefinitions(
         string gridType,
         DealerGroupName dealerGroup,
         GetColumnDefinitionOptions options)
      {
         switch (gridType.ToLower())
         {
            case "stockreport":
               return GetStockReportColumnDefs(options);
            default:
               throw new ArgumentException($"Unknown grid gridColumnTypeEnum = {gridType}");
         }
      }

      private List<CPHColDefDTO> GetStockReportColumnDefs(GetColumnDefinitionOptions options)
      {
         var columns = new List<CPHColDefDTO> {
      new CPHColDefDTO {
         headerName = "Site Name",
            field = "RetailerSiteName",
            gridColumnSectionEnum = GridColumnSectionEnum.Site,
            colId = "RetailerSiteName",
            width = 10,
            gridColumnTypeEnum =  GridColumnTypeEnum.LabelSetFilter
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Site Brand(s)",
            field = "SiteBrand"
         ,hasExplanation = true,
         gridColumnSectionEnum = GridColumnSectionEnum.Site,
            colId = "SiteBrand",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.LabelSetFilter,
            hide = true,
            provideExplanation = true
      },
      new CPHColDefDTO {
         headerName = "Region",
            field = "RegionName",
            gridColumnSectionEnum = GridColumnSectionEnum.Site,
            colId = "RegionName",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.LabelSetFilter,
            hide = true
         ,hasExplanation = true,
},

      // Vehicle (many)
      new CPHColDefDTO {
         headerName = "Reg",
         field = "VehicleReg",
         gridColumnSectionEnum = GridColumnSectionEnum.Vehicle,
         colId = "VehicleReg",
         width = 10,
         gridColumnTypeEnum = GridColumnTypeEnum.Label
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Chassis",
         field = "Chassis",
         gridColumnSectionEnum = GridColumnSectionEnum.Vehicle,
         colId = "Chassis",
         width = 10,
         gridColumnTypeEnum = GridColumnTypeEnum.Label,
         hide = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Vehicle Type",
         field = "VehicleType",
         gridColumnSectionEnum = GridColumnSectionEnum.Vehicle,
         colId = "VehicleType",
         width = 10,
         gridColumnTypeEnum = GridColumnTypeEnum.LabelSetFilter,
         hide = true,
         provideExplanation = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Body Type",
         field = "BodyType",
         gridColumnSectionEnum = GridColumnSectionEnum.Vehicle,
         colId = "BodyType",
         width = 10,
         gridColumnTypeEnum = GridColumnTypeEnum.LabelSetFilter,
         hide = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Make",
            field = "Make",
            gridColumnSectionEnum = GridColumnSectionEnum.Vehicle,
            colId = "Make",
            width = 10,
             gridColumnTypeEnum = GridColumnTypeEnum.LabelSetFilter
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Model",
            field = "Model",
            gridColumnSectionEnum = GridColumnSectionEnum.Vehicle,
            colId = "Model",
            width = 10,
             gridColumnTypeEnum = GridColumnTypeEnum.LabelSetFilter
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Model Clean",
            field = "ModelCleanedUp",
            gridColumnSectionEnum = GridColumnSectionEnum.Vehicle,
            colId = "ModelCleanedUp",
            width = 10,
             gridColumnTypeEnum = GridColumnTypeEnum.LabelSetFilter,
            hide = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "30 day Sell Rate",
            field = "ModelSellRate",
            gridColumnSectionEnum = GridColumnSectionEnum.Vehicle,
            colId = "ModelSellRate",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Number,
            hide = true,
            shouldAverageIfValue = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Stock Count",
            field = "count",
            gridColumnSectionEnum = GridColumnSectionEnum.Vehicle,
            colId = "count",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Number,
            hide = true,
            shouldAverageIfValue = true,
            shouldTotal = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Days cover",
            gridColumnSectionEnum = GridColumnSectionEnum.Vehicle,
            colId = "weeksCover",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Number1dp,
            hide = true,
            shouldAverageIfValue = true,
            provideExplanation = true,
            gridValueGetterEnum = ValueGetterEnum.daysCoverGetter
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Derivative",
            field = "Derivative",
            gridColumnSectionEnum = GridColumnSectionEnum.Vehicle,
            colId = "Derivative",
            width = 10,
            maxWidth = 350,
            gridColumnTypeEnum = GridColumnTypeEnum.Label
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Derivative wrapped",
            field = "Derivative",
            gridColumnSectionEnum = GridColumnSectionEnum.Vehicle,
            colId = "Derivative1",
            width = 10,
            maxWidth = 350,
            gridColumnTypeEnum = GridColumnTypeEnum.Label,
            hide = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Trim",
            field = "Trim",
            gridColumnSectionEnum = GridColumnSectionEnum.Vehicle,
            colId = "Trim",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Label,
            hide = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Fuel Type",
            field = "FuelType",
            gridColumnSectionEnum = GridColumnSectionEnum.Vehicle,
            colId = "FuelType",
            width = 10,
             gridColumnTypeEnum = GridColumnTypeEnum.LabelSetFilter,
            hide = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Transmission Type",
            field = "TransmissionType",
            gridColumnSectionEnum = GridColumnSectionEnum.Vehicle,
            colId = "TransmissionType",
            width = 10,
             gridColumnTypeEnum = GridColumnTypeEnum.LabelSetFilter,
            hide = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Colour",
            field = "Colour",
            gridColumnSectionEnum = GridColumnSectionEnum.Vehicle,
            colId = "Colour",
            width = 10,
             gridColumnTypeEnum = GridColumnTypeEnum.LabelSetFilter,
            hide = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Manufacturer Colour",
            field = "SpecificColour",
            gridColumnSectionEnum = GridColumnSectionEnum.Vehicle,
            colId = "SpecificColour",
            width = 10,
             gridColumnTypeEnum = GridColumnTypeEnum.LabelSetFilter,
            hide = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Owners",
            field = "Owners",
            gridColumnSectionEnum = GridColumnSectionEnum.Vehicle,
            colId = "Owners",
            width = 10,
             gridColumnTypeEnum = GridColumnTypeEnum.LabelSetFilter,
            hide = true,
            shouldAverageIfValue = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Age and Owners",
            field = "AgeAndOwners",
            gridColumnSectionEnum = GridColumnSectionEnum.Vehicle,
            colId = "AgeAndOwners",
            width = 10,
             gridColumnTypeEnum = GridColumnTypeEnum.LabelSetFilter,
            hide = true,
            gridValueGetterEnum = ValueGetterEnum.AgeAndOwnersGetter
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Odometer",
            field = "OdometerReading",
            gridColumnSectionEnum = GridColumnSectionEnum.Vehicle,
            colId = "OdometerReading",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Number,
            hide = true,
            shouldAverageIfValue = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Age Band",
            field = "AgeBand",
            gridColumnSectionEnum = GridColumnSectionEnum.Vehicle,
            colId = "AgeBand",
            width = 10,
             gridColumnTypeEnum = GridColumnTypeEnum.LabelSetFilter,
            hide = true,
            sortable = true,
            gridCellRendererEnum = CellRendererEnum.AutoTraderLozengeRenderer,
            gridComparatorEnum = ComparatorEnum.getSortOrderForAgeBand
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Registered Date",
            field = "FirstRegisteredDate",
            gridColumnSectionEnum = GridColumnSectionEnum.Vehicle,
            colId = "FirstRegisteredDate",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Date,
            hide = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Value Band",
            field = "ValueBand",
            gridColumnSectionEnum = GridColumnSectionEnum.Vehicle,
            colId = "ValueBand",
            width = 10,
             gridColumnTypeEnum = GridColumnTypeEnum.LabelSetFilter,
            hide = true,
            sortable = true,
            gridCellRendererEnum = CellRendererEnum.AutoTraderLozengeRenderer,
            gridComparatorEnum = ComparatorEnum.getSortOrderForValueBand
         ,hasExplanation = true,
},
      new CPHColDefDTO
      {
         headerName = "Portal Options",
            field = "PortalOptions",
            colId = "PortalOptions",
            gridColumnSectionEnum = GridColumnSectionEnum.Vehicle,
            maxWidth = 450,
            hide = true,
            width = 10,
             gridColumnTypeEnum = GridColumnTypeEnum.LabelSetFilter,
            provideExplanation = true
         ,hasExplanation = true,
},

      // Stock Information (6)
      new CPHColDefDTO {
         headerName = "Stock Date",
            field = "StockDate",
            gridColumnSectionEnum = GridColumnSectionEnum.StockInformation,
            colId = "StockDate",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Date,
            hide = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Days Booked In",
            field = "DaysBookedIn",
            gridColumnSectionEnum = GridColumnSectionEnum.StockInformation,
            colId = "DaysBookedIn",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Number,
            shouldAverage = true,
            sortable = true,
            hide = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Days Booked In Band",
            field = "DaysBookedInBand",
            gridColumnSectionEnum = GridColumnSectionEnum.StockInformation,
            colId = "DaysBookedInBand",
            width = 10,
             gridColumnTypeEnum = GridColumnTypeEnum.LabelSetFilter,
            hide = true,
            sortable = true,
            gridCellRendererEnum = CellRendererEnum.AutoTraderLozengeRenderer,
            gridComparatorEnum = ComparatorEnum.getSortOrderForDaysBand
         ,hasExplanation = true,
},
      
      new CPHColDefDTO {
         headerName = "Vehicle Type DMS",
            field = "VehicleTypeDesc",
            gridColumnSectionEnum = GridColumnSectionEnum.StockInformation,
            colId = "VehicleTypeDesc",
            width = 10,
             gridColumnTypeEnum = GridColumnTypeEnum.LabelSetFilter,
            hide = true,
            provideExplanation = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "New/Used",
            field = "OwnershipCondition",
            gridColumnSectionEnum = GridColumnSectionEnum.StockInformation,
            colId = "OwnershipCondition",
            width = 10,
             gridColumnTypeEnum = GridColumnTypeEnum.LabelSetFilter,
            hide = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "StockPrefix",
            field = "StockPrefix",
            gridColumnSectionEnum = GridColumnSectionEnum.StockInformation,
            colId = "StockPrefix",
            width = 10,
             gridColumnTypeEnum = GridColumnTypeEnum.LabelSetFilter,
            hide = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "StockNumber",
            field = "StockNumber",
            gridColumnSectionEnum = GridColumnSectionEnum.StockInformation,
            colId = "StockNumber",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Label,
            hide = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Stock Source",
            field = "StockSource",
            gridColumnSectionEnum = GridColumnSectionEnum.StockInformation,
            colId = "StockSource",
            width = 10,
             gridColumnTypeEnum = GridColumnTypeEnum.LabelSetFilter,
            hide = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Days To Advertise",
            field = "DaysToAdvertise",
            gridColumnSectionEnum = GridColumnSectionEnum.StockInformation,
            colId = "DaysToAdvertise",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Number,
            shouldAverageIfValue = true,
            sortable = true,
            hide = true,
            provideExplanation = true
         ,hasExplanation = true,
},
       new CPHColDefDTO {
         headerName = "Is SOR",
            field = "IsSOR",
            gridColumnSectionEnum = GridColumnSectionEnum.StockInformation,
            colId = "IsSOR",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Boolean,
            shouldAverageIfValue = false,
            sortable = true,
            hide = true,
            provideExplanation = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Image",
            field = "ImageURL",
            gridColumnSectionEnum = GridColumnSectionEnum.AdvertDetails,
            colId = "ImageURL",
            gridColumnTypeEnum = GridColumnTypeEnum.Image,
            width = 100,
            maxWidth = 100,
            hide = true,
            gridCellRendererEnum = CellRendererEnum.CustomImageCellRenderer
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Ad Link",
            gridColumnSectionEnum = GridColumnSectionEnum.AdvertDetails,
            colId = "AdLink",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Special,
            hide = true,
            gridCellRendererEnum = CellRendererEnum.CustomAdLinkRenderer,
            provideExplanation = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Attention Grabber",
            field = "AttentionGrabber",
            gridColumnSectionEnum = GridColumnSectionEnum.AdvertDetails,
            colId = "AttentionGrabber",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Label
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Attention Grabber wrapped",
            field = "AttentionGrabber",
            gridColumnSectionEnum = GridColumnSectionEnum.AdvertDetails,
            colId = "AttentionGrabber1",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Label,
            hide = true,
            cellClass = "wrapText"
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Days in Stock",
            field = "DaysInStock",
            gridColumnSectionEnum = GridColumnSectionEnum.StockInformation,
            colId = "DaysInStock",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Number,
         hide = !options?.defaultToDaysInStock,
         shouldAverage = true,
            sortable = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Days in Stock Band",
            field = "DaysInStockBand",
            gridColumnSectionEnum = GridColumnSectionEnum.StockInformation,
            colId = "DaysInStockBand",
            width = 10,
             gridColumnTypeEnum = GridColumnTypeEnum.LabelSetFilter,
            hide = true,
            sortable = true,
            gridCellRendererEnum = CellRendererEnum.AutoTraderLozengeRenderer,
            gridComparatorEnum = ComparatorEnum.getSortOrderForDaysBand
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Date on Forecourt",
            field = "DateOnForecourt",
            gridColumnSectionEnum = GridColumnSectionEnum.AdvertDetails,
            colId = "DateOnForecourt",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Date,
            hide = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Search Identifier",
            field = "WebSiteSearchIdentifier",
            gridColumnSectionEnum = GridColumnSectionEnum.AdvertDetails,
            colId = "WebSiteSearchIdentifier",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Label,
            hide = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Lifecycle status",
            field = "LifecycleStatus",
            gridColumnSectionEnum = GridColumnSectionEnum.AdvertDetails,
            colId = "LifecycleStatus",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.LabelSetFilter,
            hide = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Days Listed",
            field = "DaysListed",
            gridColumnSectionEnum = GridColumnSectionEnum.AdvertDetails,
            colId = "DaysListed",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Number,
            shouldAverage = true,
            sortable = true,
            hide = false,
            gridValueGetterEnum = ValueGetterEnum.DaysListedGetter
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "DaysListed Band",
            field = "DaysListedBand",
            gridColumnSectionEnum = GridColumnSectionEnum.AdvertDetails,
            colId = "DaysListedBand",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.LabelSetFilter,
            hide = true,
            sortable = true,
            gridCellRendererEnum = CellRendererEnum.AutoTraderLozengeRenderer,
            gridComparatorEnum = ComparatorEnum.getSortOrderForDaysBand
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Has Images?",
            field = "HasImages",
            gridColumnSectionEnum = GridColumnSectionEnum.AdvertDetails,
            colId = "HasImages",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Boolean,
            hide = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "No Images",
            field = "IsMissingImages",
            gridColumnSectionEnum = GridColumnSectionEnum.AdvertDetails,
            colId = "IsMissingImages",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Boolean,
            hide = true,
            sortable = true,
            cellClass = "agAlignCentre"
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Images Count",
            field = "ImagesCount",
            gridColumnSectionEnum = GridColumnSectionEnum.AdvertDetails,
            colId = "ImagesCount",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Number,
            hide = true,
            shouldAverageIfValue = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Images Banding",
            field = "ImagesBand",
            gridColumnSectionEnum = GridColumnSectionEnum.AdvertDetails,
            colId = "ImagesBand",
            width = 10,
             gridColumnTypeEnum = GridColumnTypeEnum.LabelSetFilter,
            hide = true,
            provideExplanation = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "NoVideo",
            field = "NoVideo",
            gridColumnSectionEnum = GridColumnSectionEnum.AdvertDetails,
            colId = "NoVideo",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Boolean,
            hide = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "No Attn Grabber",
            field = "NoAttentionGrabber",
            gridColumnSectionEnum = GridColumnSectionEnum.AdvertDetails,
            colId = "NoAttentionGrabber",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Boolean,
            hide = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Low Quality",
            field = "IsLowQuality",
            gridColumnSectionEnum = GridColumnSectionEnum.AdvertDetails,
            colId = "IsLowQuality",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Boolean,
            hide = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Last Comment",
            gridColumnSectionEnum = GridColumnSectionEnum.AdvertDetails,
            colId = "lastComment",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Label,
            hide = true,
            gridValueGetterEnum = ValueGetterEnum.LastCommentGetter,
            gridCellRendererEnum = CellRendererEnum.LastCommentRenderer,
            provideExplanation = true
         ,hasExplanation = true,
},

      new CPHColDefDTO {
         headerName = "AT Ad Status",
            hide = false,
            field = "AutotraderAdvertStatus",
            gridColumnSectionEnum = GridColumnSectionEnum.AdvertDetails,
            colId = "AutotraderAdvertStatus",
            width = 10,
             gridColumnTypeEnum = GridColumnTypeEnum.LabelSetFilter,
            provideExplanation = true
         ,hasExplanation = true,
},


      new CPHColDefDTO {
         headerName = "Advertiser Ad Status",
            hide = true,
            field = "AdvertiserAdvertStatus",
            gridColumnSectionEnum = GridColumnSectionEnum.AdvertDetails,
            colId = "AdvertiserAdvertStatus",
            width = 10,
             gridColumnTypeEnum = GridColumnTypeEnum.LabelSetFilter,
            provideExplanation = true
         ,hasExplanation = true,
},

      //Competitor Information

      new CPHColDefDTO {
         headerName = "Total Competitors",
            shouldAverageIfValue = true,
            field = "CompetitorCount",
            gridColumnSectionEnum = GridColumnSectionEnum.CompetitorInformation,
            colId = "CompetitorCount",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Number,
            hide = true,
            provideExplanation = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Average PP",
            shouldAverageIfValue = true,
            field = "AveragePP",
            gridColumnSectionEnum = GridColumnSectionEnum.CompetitorInformation,
            colId = "AveragePP",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Percent1dp,
            hide = true,
            provideExplanation = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Highest PP",
            shouldAverageIfValue = true,
            field = "HighestPP",
            gridColumnSectionEnum = GridColumnSectionEnum.CompetitorInformation,
            colId = "HighestPP",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Percent1dp,
            hide = true,
            provideExplanation = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Lowest PP",
            shouldAverageIfValue = true,
            field = "LowestPP",
            gridColumnSectionEnum = GridColumnSectionEnum.CompetitorInformation,
            colId = "LowestPP",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Percent1dp,
            hide = true,
            provideExplanation = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Franchise Avg PP%",
            shouldAverageIfValue = true,
            field = "PPAverageFranchised",
            gridColumnSectionEnum = GridColumnSectionEnum.CompetitorInformation,
            colId = "PPAverageFranchised",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Percent1dp,
            hide = true,
            provideExplanation = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Independent Avg PP%",
            shouldAverageIfValue = true,
            field = "PPAverageIndependents",
            gridColumnSectionEnum = GridColumnSectionEnum.CompetitorInformation,
            colId = "PPAverageIndependents",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Percent1dp,
            hide = true,
            provideExplanation = true
         ,hasExplanation = true,
},


      // {
      //    headerName =  "Supermarket Avg PP%",
      //    shouldAverageIfValue =  true,
      //    field = "PPAverageSupermarkets",
      //    gridColumnSectionEnum = GridColumnSectionEnum.CompetitorInformation,
      //    colId = "PPAverageSupermarkets",
      //    width = 10,
      //    gridColumnTypeEnum = GridColumnTypeEnum.Percent1dp,
      //    hide = true,
      //    provideExplanation = (params) => this.provideExplanation(params),
      // },
      new CPHColDefDTO {
         headerName = "Private Avg PP%",
            shouldAverageIfValue = true,
            field = "PPAveragePrivates",
            gridColumnSectionEnum = GridColumnSectionEnum.CompetitorInformation,
            colId = "PPAveragePrivates",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Percent1dp,
            hide = true,
            provideExplanation = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Our PP% Rank",
            field = "OurPPRank",
            gridColumnSectionEnum = GridColumnSectionEnum.CompetitorInformation,
            colId = "OurPPRank",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Number,
            hide = true,
            provideExplanation = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Our Value Rank",
            field = "OurValueRank",
            gridColumnSectionEnum = GridColumnSectionEnum.CompetitorInformation,
            colId = "OurValueRank",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Number,
            hide = true,
            provideExplanation = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Cheapest Seller Name",
            field = "CheapestSellerName",
            gridColumnSectionEnum = GridColumnSectionEnum.CompetitorInformation,
            colId = "CheapestSellerName",
            width = 10,
             gridColumnTypeEnum = GridColumnTypeEnum.LabelSetFilter,
            hide = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Cheapest Seller Type",
            field = "CheapestSellerType",
            gridColumnSectionEnum = GridColumnSectionEnum.CompetitorInformation,
            colId = "CheapestSellerType",
            width = 10,
             gridColumnTypeEnum = GridColumnTypeEnum.LabelSetFilter,
            hide = true,
            provideExplanation = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "We are cheapest",
            field = "CheapestVehicle",
            gridColumnSectionEnum = GridColumnSectionEnum.CompetitorInformation,
            colId = "CheapestVehicle",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Boolean,
            hide = true,
            provideExplanation = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Vehicle is unique",
            field = "OnlyVehicle",
            gridColumnSectionEnum = GridColumnSectionEnum.CompetitorInformation,
            colId = "OnlyVehicle",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Boolean,
            hide = true,
            provideExplanation = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Market Position Score",
            shouldAverageIfValue = true,
            field = "MarketPositionScore",
            provideExplanation = true,
            gridColumnSectionEnum = GridColumnSectionEnum.CompetitorInformation,
            colId = "MarketPositionScore",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Number,
            hide = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Price Up Maintain Rank",
            shouldAverageIfValue = true,
            provideExplanation = true,
            field = "PriceUpMaintainRank",
            gridColumnSectionEnum = GridColumnSectionEnum.CompetitorInformation,
            colId = "PriceUpMaintainRank",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.CurrencyWithPlusMinus,
            hide = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Price Down Improve Rank",
            shouldAverageIfValue = true,
            provideExplanation = true,
            field = "PriceDownImproveRank",
            gridColumnSectionEnum = GridColumnSectionEnum.CompetitorInformation,
            colId = "PriceDownImproveRank",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.CurrencyWithPlusMinus,
            hide = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Price Change To Be Cheapest",
            shouldAverageIfValue = true,
            provideExplanation = true,
            field = "PriceToBeCheapest",
            gridColumnSectionEnum = GridColumnSectionEnum.CompetitorInformation,
            colId = "PriceToBeCheapest",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.CurrencyWithPlusMinus,
            hide = true
         ,hasExplanation = true,
},

      // Vehicle Metrics (10)

      new CPHColDefDTO {
         headerName = "Retail Rating",
            field = "RetailRating",
            shouldAverageIfValue = true,
            hide = true,
            colId = "RetailRating",
            gridColumnSectionEnum = GridColumnSectionEnum.VehicleMetrics,
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Number,
            provideExplanation = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "RR Band",
            field = "RetailRatingBand",
            colId = "RetailRatingBand",
            gridColumnSectionEnum = GridColumnSectionEnum.VehicleMetrics,
            width = 10,
            gridComparatorEnum = ComparatorEnum.getSortOrderForRetailRatingBand,
            gridCellRendererEnum = CellRendererEnum.AutoTraderLozengeRenderer,
             gridColumnTypeEnum = GridColumnTypeEnum.LabelSetFilter,
            hide = false
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Days To Sell",
            field = "DaysToSellAtCurrentSelling",
            shouldAverage = true,
            provideExplanation = true,
            colId = "DaysToSellAtCurrentSelling",
            gridColumnSectionEnum = GridColumnSectionEnum.VehicleMetrics,
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Number
         ,hasExplanation = true,
},
      new CPHColDefDTO

      {
         headerName = "Demand",
            shouldAverageIfValue = true,
            provideExplanation = true,
            field = "RetailDemand",
            gridColumnSectionEnum = GridColumnSectionEnum.VehicleMetrics,
            colId = "RetailDemand",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Percent,
            hide = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Supply",
            shouldAverageIfValue = true,
            provideExplanation = true,
            field = "RetailSupply",
            gridColumnSectionEnum = GridColumnSectionEnum.VehicleMetrics,
            colId = "RetailSupply",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Percent,
            hide = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Market condition",
            shouldAverageIfValue = true,
            field = "RetailMarketCondition",
            gridColumnSectionEnum = GridColumnSectionEnum.VehicleMetrics,
            colId = "RetailMarketCondition",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Percent,
            hide = true,
            provideExplanation = true
         ,hasExplanation = true,
},

      new CPHColDefDTO {
         headerName = "National Market condition",
            shouldAverageIfValue = true,
            provideExplanation = true,
            field = "NationalRetailMarketCondition",
            gridColumnSectionEnum = GridColumnSectionEnum.VehicleMetrics,
            colId = "NationalRetailMarketCondition",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Percent,
            hide = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "National Retail Rating",
            hide = true,
            field = "NationalRetailRating",
            shouldAverageIfValue = true,
            gridColumnSectionEnum = GridColumnSectionEnum.VehicleMetrics,
            colId = "NationalRetailRating",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Number
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "National Retail Rating Band",
            field = "NationalRetailRatingBand",
            hide = true,
            gridColumnSectionEnum = GridColumnSectionEnum.VehicleMetrics,
            colId = "NationalRetailRatingBand",
            width = 10,
             gridColumnTypeEnum = GridColumnTypeEnum.LabelSetFilter
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "National Days To Sell",
            shouldAverageIfValue = true,
            field = "NationalRetailDaysToSell",
            hide = true,
            shouldAverage = true,
            gridColumnSectionEnum = GridColumnSectionEnum.VehicleMetrics,
            colId = "NationalRetailDaysToSell",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Number
         ,hasExplanation = true,
},


      //Costings  (6)
      new CPHColDefDTO {
         headerName = "Cost Price",
            field = "SIV",
            gridColumnSectionEnum = GridColumnSectionEnum.Costings,
            colId = "SIV",
            width = 10,
            hide = true,
            shouldAverageIfValue = true,
            gridColumnTypeEnum = GridColumnTypeEnum.Currency,
            provideExplanation = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Orig Purch Price",
            field = "OriginalPurchasePrice",
            shouldAverageIfValue = true,
            gridColumnSectionEnum = GridColumnSectionEnum.Costings,
            colId = "OriginalPurchasePrice",
            width = 10,
            hide = true,
            gridColumnTypeEnum = GridColumnTypeEnum.Currency
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Prep Cost",
            shouldAverageIfValue = true,
            field = "PrepCost",
            gridColumnSectionEnum = GridColumnSectionEnum.Costings,
            colId = "PrepCost",
            hide = true,
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Currency,
            provideExplanation = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Priced Profit",
            hide = true,
            gridColumnSectionEnum = GridColumnSectionEnum.Costings,
            colId = "PricedProfit",
            shouldAverageIfValue = true,
            field = "PricedProfit",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Currency,
            provideExplanation = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Vat Qualifying",
            shouldAverageIfValue = true,
            hide = true,
            gridColumnSectionEnum = GridColumnSectionEnum.Costings,
            colId = "IsVatQ",
            field = "IsVatQ",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Boolean
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "HaveProfit",
            hide = true,
            gridColumnSectionEnum = GridColumnSectionEnum.Costings,
            colId = "HaveProfit",
            field = "PricedProfit",
            gridValueGetterEnum = ValueGetterEnum.PriceProfitGetter, //  (parms) => parms.data?.PricedProfit !== null,
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Boolean,
            provideExplanation = true
         ,hasExplanation = true,
},

      new CPHColDefDTO
      {
         headerName = "Low Price",
         hide = true,
         gridColumnSectionEnum = GridColumnSectionEnum.Costings,
         colId = "PriceLow",
         field = "PriceLow",
         width = 10,
         gridColumnTypeEnum = GridColumnTypeEnum.Currency
         ,hasExplanation = true,
},

       new CPHColDefDTO
      {
         headerName = "Great Price",
         hide = true,
         gridColumnSectionEnum = GridColumnSectionEnum.Costings,
         colId = "PriceGreat",
         field = "PriceGreat",
         width = 10,
         gridColumnTypeEnum = GridColumnTypeEnum.Currency
         ,hasExplanation = true,
},
       new CPHColDefDTO
      {
         headerName = "Good Price",
         hide = true,
         gridColumnSectionEnum = GridColumnSectionEnum.Costings,
         colId = "PriceGood",
         field = "PriceGood",
         width = 10,
         gridColumnTypeEnum = GridColumnTypeEnum.Currency
         ,hasExplanation = true,
},

       new CPHColDefDTO
      {
         headerName = "Fair Price",
         hide = true,
         gridColumnSectionEnum = GridColumnSectionEnum.Costings,
         colId = "PriceFair",
         field = "PriceFair",
         width = 10,
         gridColumnTypeEnum = GridColumnTypeEnum.Currency
         ,hasExplanation = true,
},

       new CPHColDefDTO
      {
         headerName = "High Price",
         hide = true,
         gridColumnSectionEnum = GridColumnSectionEnum.Costings,
         colId = "PriceHigh",
         field = "PriceHigh",
         width = 10,
         gridColumnTypeEnum = GridColumnTypeEnum.Currency
         ,hasExplanation = true,
},

        new CPHColDefDTO
      {
         headerName = "Low Price Change",
         hide = true,
         gridColumnSectionEnum = GridColumnSectionEnum.Costings,
         colId = "PriceChangeLow",
         field = "PriceChangeLow",
         width = 10,
         gridColumnTypeEnum = GridColumnTypeEnum.Currency
         ,hasExplanation = true,
},

              new CPHColDefDTO
      {
         headerName = "Great Price Change",
         hide = true,
         gridColumnSectionEnum = GridColumnSectionEnum.Costings,
         colId = "PriceChangeGreat",
         field = "PriceChangeGreat",
         width = 10,
         gridColumnTypeEnum = GridColumnTypeEnum.Currency
         ,hasExplanation = true,
},
       new CPHColDefDTO
      {
         headerName = "Good Price Change",
         hide = true,
         gridColumnSectionEnum = GridColumnSectionEnum.Costings,
         colId = "PriceChangeGood",
         field = "PriceChangeGood",
         width = 10,
         gridColumnTypeEnum = GridColumnTypeEnum.Currency
         ,hasExplanation = true,
},

       new CPHColDefDTO
      {
         headerName = "Fair Price Change",
         hide = true,
         gridColumnSectionEnum = GridColumnSectionEnum.Costings,
         colId = "PriceChangeFair",
         field = "PriceChangeFair",
         width = 10,
         gridColumnTypeEnum = GridColumnTypeEnum.Currency
         ,hasExplanation = true,
},

       new CPHColDefDTO
      {
         headerName = "High Price Change",
         hide = true
         ,hasExplanation = true,
         gridColumnSectionEnum = GridColumnSectionEnum.Costings,
         colId = "PriceChangeHigh",
         field = "PriceChangeHigh",
         width = 10,
         gridColumnTypeEnum = GridColumnTypeEnum.Currency
      },


      //Valuation (12)

      new CPHColDefDTO {
         headerName = "Valn Adjusted PartEx",
            gridColumnSectionEnum = GridColumnSectionEnum.Valuation,
            field = "ValuationAdjPartEx",
            shouldAverageIfValue = true,
            hide = true,
            colId = "ValuationAdjPartEx",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Currency,
            provideExplanation = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Valn Adjusted Trade",
            gridColumnSectionEnum = GridColumnSectionEnum.Valuation,
            field = "ValuationAdjTrade",
            shouldAverageIfValue = true,
            hide = true,
            colId = "ValuationAdjTrade",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Currency,
            provideExplanation = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Valn Adjusted Private",
            gridColumnSectionEnum = GridColumnSectionEnum.Valuation,
            field = "ValuationAdjPrivate",
            shouldAverageIfValue = true,
            hide = true,
            colId = "ValuationAdjPrivate",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Currency,
            provideExplanation = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Valn Adjusted RetailExVat",
            gridColumnSectionEnum = GridColumnSectionEnum.Valuation,
            field = "ValuationAdjRetailExVat",
            shouldAverageIfValue = true,
            hide = true,
            colId = "ValuationAdjRetailExVat",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Currency,
            provideExplanation = true
         ,hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Valn Adjusted Retail",
            gridColumnSectionEnum = GridColumnSectionEnum.Valuation,
            field = "ValuationAdjRetail",
            shouldAverageIfValue = true,
            hide = true,
            colId = "ValuationAdjRetail",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Currency,
            provideExplanation = true
         ,hasExplanation = true,
},
      new CPHColDefDTO

      {
         headerName = "Valuation",
            gridColumnSectionEnum = GridColumnSectionEnum.Valuation,
            field = "RelevantValuation",
            shouldAverageIfValue = true,
            colId = "RelevantValuation",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Currency,
            provideExplanation = true,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "This Vehicle Valn vs Av.",
            hide = true,
            gridColumnSectionEnum = GridColumnSectionEnum.Valuation,
            field = "ThisVehicleValnVsAverage",
            shouldAverageIfValue = true,
            colId = "ThisVehicleValnVsAverage",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.CurrencyWithPlusMinus,
            provideExplanation = true,
         hasExplanation = true,
},

      new CPHColDefDTO
      {
         headerName = "Valn Adjusted +30days",
            shouldAverageIfValue = true,
            hide = true,
            field = "ValuationMonthPlus1",
            colId = "ValuationMonthPlus1",
            gridColumnSectionEnum = GridColumnSectionEnum.Valuation,
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Currency,
            provideExplanation = true,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Valn Adjusted +60days",
            shouldAverageIfValue = true,
            hide = true,
            field = "ValuationMonthPlus2",
            colId = "ValuationMonthPlus2",
            gridColumnSectionEnum = GridColumnSectionEnum.Valuation,
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Currency,
            provideExplanation = true,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Valn Adjusted +90days",
            shouldAverageIfValue = true,
            hide = true,
            field = "ValuationMonthPlus3",
            colId = "ValuationMonthPlus3",
            gridColumnSectionEnum = GridColumnSectionEnum.Valuation,
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Currency,
            provideExplanation = true,
         hasExplanation = true,
},

      new CPHColDefDTO
      {
         headerName = "Valn Change M+1",
            shouldAverageIfValue = true,
            hide = true,
            field = "ValuationChangeMonthPlus1",
            colId = "ValuationChangeMonthPlus1",
            gridColumnSectionEnum = GridColumnSectionEnum.Valuation,
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Currency,
            provideExplanation = true,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Valn Change M+2",
            shouldAverageIfValue = true,
            hide = true,
            field = "ValuationChangeMonthPlus2",
            colId = "ValuationChangeMonthPlus2",
            gridColumnSectionEnum = GridColumnSectionEnum.Valuation,
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Currency,
            provideExplanation = true,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Valn Change M+3",
            shouldAverageIfValue = true,
            hide = true,
            field = "ValuationChangeMonthPlus3",
            colId = "ValuationChangeMonthPlus3",
            gridColumnSectionEnum = GridColumnSectionEnum.Valuation,
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Currency,
            provideExplanation = true,
         hasExplanation = true,
},

      // Valuation - Average Spec (5) 

      new CPHColDefDTO

      {
         headerName = "Valn Mkt Avg PartEx",
            gridColumnSectionEnum = GridColumnSectionEnum.ValuationAverageSpec,
            hide = true,
            field = "ValuationMktAvPartEx",
            shouldAverageIfValue = true,
            colId = "ValuationMktAvPartEx",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Currency,
            provideExplanation = true,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Valn Mkt Avg Trade",
            gridColumnSectionEnum = GridColumnSectionEnum.ValuationAverageSpec,
            hide = true,
            field = "ValuationMktAvTrade",
            shouldAverageIfValue = true,
            colId = "ValuationMktAvTrade",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Currency,
            provideExplanation = true,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Valn Mkt Avg Private",
            gridColumnSectionEnum = GridColumnSectionEnum.ValuationAverageSpec,
            hide = true,
            field = "ValuationMktAvPrivate",
            shouldAverageIfValue = true,
            colId = "ValuationMktAvPrivate",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Currency,
            provideExplanation = true,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Valn Mkt Avg RetailExVat",
            gridColumnSectionEnum = GridColumnSectionEnum.ValuationAverageSpec,
            hide = true,
            field = "ValuationMktAvRetailExVat",
            shouldAverageIfValue = true,
            colId = "ValuationMktAvRetailExVat",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Currency,
            provideExplanation = true,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Valn Mkt Avg Retail",
            gridColumnSectionEnum = GridColumnSectionEnum.ValuationAverageSpec,
            hide = true,
            field = "ValuationMktAvRetail",
            shouldAverageIfValue = true,
            colId = "ValuationMktAvRetail",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Currency,
            provideExplanation = true,
         hasExplanation = true,
},

      //Advertised Price (7)  +2 for VIndis
      new CPHColDefDTO {
         headerName = "Advertised Price",
            field = "AdvertisedPrice",
            gridColumnSectionEnum = GridColumnSectionEnum.AdvertisedPrice,
            colId = "SuppliedPrice",
            mappedField = "AdvertisedPrice",
            shouldAverageIfValue = true,
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Currency,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Advertised Price Excl Admin",
            field = "AdvertisedPriceExclAdminFee",
            gridColumnSectionEnum = GridColumnSectionEnum.AdvertisedPrice,
            colId = "AdvertisedPriceExclAdminFee",
            shouldAverageIfValue = true,
            hide = true,
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Currency,
            provideExplanation = true,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "IncludingVat",
            field = "IncludingVat",
            gridColumnSectionEnum = GridColumnSectionEnum.AdvertisedPrice,
            colId = "IncludingVat",
            gridColumnTypeEnum = GridColumnTypeEnum.Boolean,
            width = 10,
            hide = true,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Price Position",
            shouldAverageIfValue = true,
            field = "PricePosition",
            gridColumnTypeEnum = GridColumnTypeEnum.Percent1dp,
            gridColumnSectionEnum = GridColumnSectionEnum.AdvertisedPrice,
            colId = "PricePosition",
            width = 10,
            provideExplanation = true,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Strategy Price Calculated?",
            field = "StrategyPriceHasBeenCalculated",
            shouldAverageIfValue = true,
            gridColumnSectionEnum = GridColumnSectionEnum.AdvertisedPrice,
            colId = "StrategyPriceHasBeenCalculated",
            width = 10,
            hide = true,
            gridColumnTypeEnum = GridColumnTypeEnum.Boolean,
            provideExplanation = true,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Strategy Price",
            field = "StrategyPrice",
            shouldAverageIfValue = true,
            gridColumnSectionEnum = GridColumnSectionEnum.AdvertisedPrice,
            colId = "StrategyPrice",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Currency,
            provideExplanation = true,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Advertised Vs Strategy",
            shouldAverageIfValue = true,
            gridColumnSectionEnum = GridColumnSectionEnum.AdvertisedPrice,
            colId = "VsStrategyPrice",
            field = "VsStrategyPrice",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.CurrencyWithPlusMinus,
            provideExplanation = true,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Vs Strategy Band",
            field = "VsStrategyBanding",
            gridValueGetterEnum = ValueGetterEnum.formatTestStrategyBandLabel,
            colId = "VsStrategyBanding",
            width = 7,
             gridColumnTypeEnum = GridColumnTypeEnum.LabelSetFilter,
            gridColumnSectionEnum = GridColumnSectionEnum.AdvertisedPrice,
            gridCellRendererEnum = CellRendererEnum.AutoTraderLozengeRenderer,
            cellClass = "agAlignCentre",
            sortable = true,
            gridComparatorEnum = ComparatorEnum.getSortOrderForVsStrategyBanding,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Vs Test Strategy Band",
            field = "VsTestStrategyBanding",
            gridValueGetterEnum = ValueGetterEnum.formatTestStrategyBandLabel,
            colId = "VsTestStrategyBanding",
            width = 7,
             gridColumnTypeEnum = GridColumnTypeEnum.LabelSetFilter,
            gridColumnSectionEnum = GridColumnSectionEnum.AdvertisedPrice,
            gridCellRendererEnum = CellRendererEnum.AutoTraderLozengeRenderer,
            cellClass = "agAlignCentre",
            sortable = true,
            gridComparatorEnum = ComparatorEnum.getSortOrderForVsStrategyBanding,
         hasExplanation = true,
},
      new CPHColDefDTO
      {
         headerName = "Price Indicator",
            field = "PriceIndicatorRatingAtCurrentSelling",
            mappedField = "PriceIndicatorRatingAtCurrentSelling",
            colId = "PriceIndicatorRating",
            gridCellRendererEnum = CellRendererEnum.autoTraderPriceIndicatorRenderer,
            width = 7,
            minwidth = 80,
            gridColumnTypeEnum = GridColumnTypeEnum.LabelSetFilter,
            cellClass = "agAlignCentre",
            sortable = true,
            gridColumnSectionEnum = GridColumnSectionEnum.AdvertisedPrice,
            gridComparatorEnum = ComparatorEnum.getSortOrderForPriceIndicator,
            provideExplanation = true,
         hasExplanation = true,
},

      // Advert Performance (10)

      new CPHColDefDTO {
         headerName = "Perf Rtg Score",
            field = "PerfRatingScore",
            shouldAverageIfValue = true,
            hide = true,
            gridColumnSectionEnum = GridColumnSectionEnum.AdvertPerformance,
            colId = "PerfRatingScore",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Number,
            provideExplanation = true,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Perf Rtg",
            field = "PerfRating",
            shouldAverageIfValue = true,
            hide = true,
            gridColumnSectionEnum = GridColumnSectionEnum.AdvertPerformance,
            colId = "PerformanceRating",
            width = 50,
            minWidth = 50,
            gridColumnTypeEnum = GridColumnTypeEnum.Number,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Perf Rtg Band",
            field = "PerformanceRatingScoreBand",
            gridColumnSectionEnum = GridColumnSectionEnum.AdvertPerformance,
            colId = "PerformanceRatingScoreBand",
            width = 10,
             gridColumnTypeEnum = GridColumnTypeEnum.LabelSetFilter,
            hide = true,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Perf Rtg",
            field = "PerfRating",
             gridColumnTypeEnum = GridColumnTypeEnum.LabelSetFilter,
            gridValueGetterEnum = ValueGetterEnum.PerfRating,
            colId = "PerfRating",
            provideExplanation = true,
            minwidth = 100,
            width = 10,
            gridCellRendererEnum = CellRendererEnum.AutoTraderLozengeRenderer,
            cellClass = "agAlignCentre",
            sortable = true,
            gridColumnSectionEnum = GridColumnSectionEnum.AdvertPerformance,
            gridComparatorEnum = ComparatorEnum.getSortOrderForPerfRating,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Searches Yesterday",
            shouldAverageIfValue = true,
            field = "SearchViewsYest",
            shouldTotal = true,
            shouldAverage = true,
            gridColumnSectionEnum = GridColumnSectionEnum.AdvertPerformance,
            colId = "SearchViewsYest",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Number,
            hide = true,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Ad views Yesterday",
            shouldAverageIfValue = true,
            field = "AdvertViewsYest",
            shouldTotal = true,
            shouldAverage = true,
            gridColumnSectionEnum = GridColumnSectionEnum.AdvertPerformance,
            colId = "AdvertViewsYest",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Number,
            hide = true,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Searches 7 Day",
            shouldAverageIfValue = true,
            provideExplanation = true,
            field = "SearchViews7Day",
            shouldTotal = true,
            shouldAverage = true,
            gridColumnSectionEnum = GridColumnSectionEnum.AdvertPerformance,
            colId = "SearchViews7Day",
            hide = true,
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Number,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Ad views 7 Day",
            shouldAverageIfValue = true,
            provideExplanation = true,
            field = "AdvertViews7Day",
            shouldTotal = true,
            shouldAverage = true,
            gridColumnSectionEnum = GridColumnSectionEnum.AdvertPerformance,
            colId = "AdvertViews7Day",
            hide = true,
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Number,
         hasExplanation = true,
},
      new CPHColDefDTO

      {
         headerName = "Daily Search Views Last7",
            gridCellRendererEnum = CellRendererEnum.agSparklineCellRenderer,
            cellRendererParams = CellRendererParamsEnum.ProvideSparkLineParams,
            field = "DailySearchViewsLast7",
            gridColumnSectionEnum = GridColumnSectionEnum.AdvertPerformance,
            gridValueGetterEnum = ValueGetterEnum.SparkLineDataGetter,
            colId = "DailySearchViewsLast7",
            width = 50,
            hide = true,
            provideExplanation = true,
         hasExplanation = true,
},
      new CPHColDefDTO

      {
         headerName = "Daily Advert Views Last7",
            gridCellRendererEnum = CellRendererEnum.agSparklineCellRenderer,
            cellRendererParams = CellRendererParamsEnum.ProvideSparkLineParams,
            field = "DailyAdvertViewsLast7",
            gridColumnSectionEnum = GridColumnSectionEnum.AdvertPerformance,
            gridValueGetterEnum = ValueGetterEnum.SparkLineDataGetter,
            colId = "DailyAdvertViewsLast7",
            width = 50,
            hide = true,
            provideExplanation = true,
         hasExplanation = true,
},


      //Opt outs (4)
      new CPHColDefDTO
      {
         headerName = "Is Opted Out",
            hide = true,
            field = "IsOptedOut",
            gridColumnSectionEnum = GridColumnSectionEnum.OptOuts,
            colId = "IsOptedOut",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Boolean,
            provideExplanation = true,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Opted Out By",
            hide = true,
            field = "OptedOutBy",
            gridColumnSectionEnum = GridColumnSectionEnum.OptOuts,
            colId = "OptedOutBy",
            width = 10,
             gridColumnTypeEnum = GridColumnTypeEnum.LabelSetFilter,
            provideExplanation = true,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "When Opted Out",
            hide = true,
            field = "WhenOptedOut",
            gridColumnSectionEnum = GridColumnSectionEnum.OptOuts,
            colId = "WhenOptedOut",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Date,
            provideExplanation = true,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Opted Out Until",
            hide = true,
            field = "OptedOutUntil",
            gridColumnSectionEnum = GridColumnSectionEnum.OptOuts,
            colId = "OptedOutUntil",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Date,
            provideExplanation = true,
         hasExplanation = true,
},

         new CPHColDefDTO {
         headerName = "Opt Out Type",
            hide = true,
            field = "AutoOptOutType",
            gridColumnSectionEnum = GridColumnSectionEnum.OptOuts,
            colId = "AutoOptOutType",
            width = 30,
            gridColumnTypeEnum = GridColumnTypeEnum.LabelSetFilter,
            provideExplanation = true,
         hasExplanation = true,
},

      //Price Changes (6)

      new CPHColDefDTO
      {
         headerName = "Total Daily Price Moves Value",
            shouldAverageIfValue = true,
            hide = true,
            field = "TotalPriceChanges",
            gridColumnSectionEnum = GridColumnSectionEnum.PriceChanges,
            colId = "TotalPriceChanges",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Currency,
            provideExplanation = true,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Total Daily Price Moves Quantity",
            shouldAverageIfValue = true,
            hide = true,
            field = "DailyPriceMovesCount",
            gridColumnSectionEnum = GridColumnSectionEnum.PriceChanges,
            colId = "DailyPriceMovesCount",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Number,
            provideExplanation = true,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Most Recent Price Change Value",
            shouldAverageIfValue = true,
            hide = true,
            field = "MostRecentDailyPriceMove",
            gridColumnSectionEnum = GridColumnSectionEnum.PriceChanges,
            colId = "MostRecentDailyPriceMove",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Currency,
            provideExplanation = true,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Most Recent Price Change Date",
            shouldAverageIfValue = true,
            hide = true,
            field = "MostRecentDailyPriceMoveDate",
            gridColumnSectionEnum = GridColumnSectionEnum.PriceChanges,
            colId = "MostRecentDailyPriceMoveDate",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Date,
            provideExplanation = true,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Days Since Price Change",
            shouldAverageIfValue = true,
            hide = true,
            field = "DaysSinceMostRecentPriceMove",
            gridColumnSectionEnum = GridColumnSectionEnum.PriceChanges,
            colId = "DaysSinceMostRecentPriceMove",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Number,
            provideExplanation = true,
         hasExplanation = true,
},

      // Price Changes Manual Via Spark (6) 

      new CPHColDefDTO {
         headerName = "Last Price Change Value",
            shouldAverageIfValue = true,
            hide = true,
            field = "LastPriceChangeValue",
            gridColumnSectionEnum = GridColumnSectionEnum.PriceChangesManually,
            colId = "LastPriceChangeValue",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Currency,
            provideExplanation = true,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Days Since Last Price Change",
            shouldAverageIfValue = true,
            hide = true,
            field = "DaysSinceLastPriceChange",
            gridColumnSectionEnum = GridColumnSectionEnum.PriceChangesManually,
            colId = "DaysSinceLastPriceChange",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Number,
            provideExplanation = true,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "When Price Last Manually Changed",
            hide = true,
            field = "WhenPriceLastManuallyChanged",
            gridColumnSectionEnum = GridColumnSectionEnum.PriceChangesManually,
            colId = "WhenPriceLastManuallyChanged",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Date,
            provideExplanation = true,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Who Last Manually Changed",
            hide = true,
            field = "WhoLastManuallyChanged",
            gridColumnSectionEnum = GridColumnSectionEnum.PriceChangesManually,
            colId = "WhoLastManuallyChanged",
            width = 10,
             gridColumnTypeEnum = GridColumnTypeEnum.LabelSetFilter,
            provideExplanation = true,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Total Manual Price Changes Count",
            shouldAverageIfValue = true,
            hide = true,
            field = "TotalManualPriceChangesCount",
            gridColumnSectionEnum = GridColumnSectionEnum.PriceChangesManually,
            colId = "TotalManualPriceChangesCount",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Number,
            provideExplanation = true,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Total Manual Price Changes Value",
            shouldAverageIfValue = true,
            hide = true,
            field = "TotalManualPriceChangesValue",
            gridColumnSectionEnum = GridColumnSectionEnum.PriceChangesManually,
            colId = "TotalManualPriceChangesValue",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Currency,
            provideExplanation = true,
         hasExplanation = true,
},

      // BCA INFORMATION (11) 

      new CPHColDefDTO {
         headerName = "Bca VIN",
            hide = true,
            field = "BcaVin",
            gridColumnSectionEnum = GridColumnSectionEnum.BCAInformation,
            colId = "BcaVin",
            width = 10,
             gridColumnTypeEnum = GridColumnTypeEnum.LabelSetFilter,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Bca Mileage",
            hide = true,
            field = "BcaMileage",
            gridColumnSectionEnum = GridColumnSectionEnum.BCAInformation,
            colId = "BcaMileage",
            width = 12,
            gridColumnTypeEnum = GridColumnTypeEnum.Number,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Days on All Sites",
            hide = true,
            field = "DaysOnAllSites",
            gridColumnSectionEnum = GridColumnSectionEnum.BCAInformation,
            colId = "DaysOnAllSites",
            width = 15,
            gridColumnTypeEnum = GridColumnTypeEnum.Number,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Number of Previous Sales",
            hide = true,
            field = "NumberOfPreviousSales",
            gridColumnSectionEnum = GridColumnSectionEnum.BCAInformation,
            colId = "NumberOfPreviousSales",
            width = 20,
            gridColumnTypeEnum = GridColumnTypeEnum.Number,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "V5 Status",
            hide = true,
            field = "V5Status",
            gridColumnSectionEnum = GridColumnSectionEnum.BCAInformation,
            colId = "V5Status",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Boolean,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Service History",
            hide = true,
            field = "ServiceHistory",
            gridColumnSectionEnum = GridColumnSectionEnum.BCAInformation,
            colId = "ServiceHistory",
            width = 12,
            gridColumnTypeEnum = GridColumnTypeEnum.Boolean,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Runner",
            hide = true,
            field = "Runner",
            gridColumnSectionEnum = GridColumnSectionEnum.BCAInformation,
            colId = "Runner",
            width = 10,
             gridColumnTypeEnum = GridColumnTypeEnum.LabelSetFilter,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Sales Comment",
            hide = true,
            field = "SalesComment",
            gridColumnSectionEnum = GridColumnSectionEnum.BCAInformation,
            colId = "SalesComment",
            width = 25,
             gridColumnTypeEnum = GridColumnTypeEnum.LabelSetFilter,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Hold Date",
            hide = true,
            field = "HoldDate",
            gridColumnSectionEnum = GridColumnSectionEnum.BCAInformation,
            colId = "HoldDate",
            width = 15,
            gridColumnTypeEnum = GridColumnTypeEnum.Date,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Hold Code",
            hide = true,
            field = "HoldCode",
            gridColumnSectionEnum = GridColumnSectionEnum.BCAInformation,
            colId = "HoldCode",
            width = 10,
             gridColumnTypeEnum = GridColumnTypeEnum.LabelSetFilter,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Hold Description",
            hide = true,
            field = "HoldDescription",
            gridColumnSectionEnum = GridColumnSectionEnum.BCAInformation,
            colId = "HoldDescription",
            width = 20,
             gridColumnTypeEnum = GridColumnTypeEnum.LabelSetFilter,
         hasExplanation = true,
},
      
      // Today's Price Change (7)
      new CPHColDefDTO {
         headerName = "New Price",
            shouldAverageIfValue = true,
            hide = true,
            field = "NewPrice",
            colId = "NewPrice",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Currency,
            gridColumnSectionEnum = GridColumnSectionEnum.TodaysPriceChange,
            provideExplanation = true,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "New Price excl AdminFee",
            shouldAverageIfValue = true,
            hide = true,
            field = "NewPriceExAdminFee",
            colId = "NewPriceExAdminFee",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Currency,
            gridColumnSectionEnum = GridColumnSectionEnum.TodaysPriceChange,
            provideExplanation = true,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "New PP%",
            shouldAverageIfValue = true,
            hide = true,
            field = "NewPP",
            colId = "NewPP",
            width = 10,
            gridColumnTypeEnum = GridColumnTypeEnum.Percent1dp,
            gridColumnSectionEnum = GridColumnSectionEnum.TodaysPriceChange,
            provideExplanation = true,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Price Change",
            shouldAverageIfValue = true,
            hide = true,
            field = "todayPriceChange",
            colId = "todayPriceChange",
            width = 10,
            gridColumnSectionEnum = GridColumnSectionEnum.TodaysPriceChange,
            gridColumnTypeEnum = GridColumnTypeEnum.Currency,
            provideExplanation = true,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Is Small Change",
            hide = true,
            field = "IsSmallPriceChange",
            colId = "IsSmallPriceChange",
            width = 10,
            gridColumnSectionEnum = GridColumnSectionEnum.TodaysPriceChange,
            gridColumnTypeEnum = GridColumnTypeEnum.Boolean,
            provideExplanation = true,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "New Days To Sell",
            shouldAverageIfValue = true,
            hide = true,
            field = "DaysToSellAtNewPrice",
            colId = "DaysToSellAtNewPrice",
            width = 10,
            gridColumnSectionEnum = GridColumnSectionEnum.TodaysPriceChange,
            gridColumnTypeEnum = GridColumnTypeEnum.Number,
            provideExplanation = true,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "New Price Indicator",
            field = "PriceIndicatorAtNewPrice",
            colId = "PriceIndicatorAtNewPrice",
            hide = true,
            gridColumnSectionEnum = GridColumnSectionEnum.TodaysPriceChange,
            gridCellRendererEnum = CellRendererEnum.autoTraderPriceIndicatorRenderer,
            width = 7,
             gridColumnTypeEnum = GridColumnTypeEnum.LabelSetFilter,
            cellClass = "agAlignCentre",
            sortable = true,
            gridComparatorEnum = ComparatorEnum.getSortOrderForPriceIndicator,
            provideExplanation = true,
         hasExplanation = true,
},

      // Analysis (16)

      new CPHColDefDTO
      {
         headerName = "Count",
            hide = true,
            field = "count",
            shouldTotal = true,
            colId = "count",
            gridColumnSectionEnum = GridColumnSectionEnum.Analysis,
            maxWidth = 50,
            gridColumnTypeEnum = GridColumnTypeEnum.Number,
         hasExplanation = true,
},

      //By Retail Rating
      new CPHColDefDTO {
         headerName = "RR <20",
            hide = true,
            field = "rrU20Count",
            shouldTotal = true,
            colId = "rrU20Count",
            gridColumnSectionEnum = GridColumnSectionEnum.Analysis,
            maxWidth = 50,
            gridColumnTypeEnum = GridColumnTypeEnum.Number,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "RR <40",
            hide = true,
            field = "rrU40Count",
            shouldTotal = true,
            colId = "rrU40Count",
            gridColumnSectionEnum = GridColumnSectionEnum.Analysis,
            maxWidth = 50,
            gridColumnTypeEnum = GridColumnTypeEnum.Number,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "RR <60",
            hide = true,
            field = "rrU60Count",
            shouldTotal = true,
            colId = "rrU60Count",
            gridColumnSectionEnum = GridColumnSectionEnum.Analysis,
            maxWidth = 50,
            gridColumnTypeEnum = GridColumnTypeEnum.Number,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "RR <80",
            hide = true,
            field = "rrU80Count",
            shouldTotal = true,
            colId = "rrU80Count",
            gridColumnSectionEnum = GridColumnSectionEnum.Analysis,
            maxWidth = 50,
            gridColumnTypeEnum = GridColumnTypeEnum.Number,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "RR 80+",
            hide = true,
            field = "rrO80Count",
            shouldTotal = true,
            colId = "rrO80Count",
            gridColumnSectionEnum = GridColumnSectionEnum.Analysis,
            maxWidth = 50,
            gridColumnTypeEnum = GridColumnTypeEnum.Number,
         hasExplanation = true,
},
      new CPHColDefDTO

      //By Days Listed
      {
         headerName = "DL <20",
            hide = true,
            field = "dlU20Count",
            shouldTotal = true,
            colId = "dlU20Count",
            gridColumnSectionEnum = GridColumnSectionEnum.Analysis,
            maxWidth = 50,
            gridColumnTypeEnum = GridColumnTypeEnum.Number,
      },
      new CPHColDefDTO {
         headerName = "DL <40",
            hide = true,
            field = "dlU40Count",
            shouldTotal = true,
            colId = "dlU40Count",
            gridColumnSectionEnum = GridColumnSectionEnum.Analysis,
            maxWidth = 50,
            gridColumnTypeEnum = GridColumnTypeEnum.Number,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "DL <60",
            hide = true,
            field = "dlU60Count",
            shouldTotal = true,
            colId = "dlU60Count",
            gridColumnSectionEnum = GridColumnSectionEnum.Analysis,
            maxWidth = 50,
            gridColumnTypeEnum = GridColumnTypeEnum.Number,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "DL 60+",
            hide = true,
            field = "dlO60Count",
            shouldTotal = true,
            colId = "dlO60Count",
            gridColumnSectionEnum = GridColumnSectionEnum.Analysis,
            maxWidth = 50,
            gridColumnTypeEnum = GridColumnTypeEnum.Number,
         hasExplanation = true,
},
      new CPHColDefDTO

      //By Strategy
      {
         headerName = "No strategy",
            hide = true,
            field = "noStrategyCount",
            shouldTotal = true,
            colId = "noStrategyCount",
            gridColumnSectionEnum = GridColumnSectionEnum.Analysis,
            maxWidth = 80,
            gridColumnTypeEnum = GridColumnTypeEnum.Number,
      },
      new CPHColDefDTO {
         headerName = "V. Under Strategy",
            hide = true,
            field = "veryUnderStrategyCount",
            shouldTotal = true,
            colId = "veryUnderStrategyCount",
            gridColumnSectionEnum = GridColumnSectionEnum.Analysis,
            maxWidth = 80,
            gridColumnTypeEnum = GridColumnTypeEnum.Number,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Under Strategy",
            hide = true,
            field = "underStrategyCount",
            shouldTotal = true,
            colId = "underStrategyCount",
            gridColumnSectionEnum = GridColumnSectionEnum.Analysis,
            maxWidth = 80,
            gridColumnTypeEnum = GridColumnTypeEnum.Number,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "On Strategy",
            hide = true,
            field = "onStrategyCount",
            shouldTotal = true,
            colId = "onStrategyCount",
            gridColumnSectionEnum = GridColumnSectionEnum.Analysis,
            maxWidth = 80,
            gridColumnTypeEnum = GridColumnTypeEnum.Number,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "Over Strategy",
            hide = true,
            field = "overStrategyCount",
            shouldTotal = true,
            colId = "overStrategyCount",
            gridColumnSectionEnum = GridColumnSectionEnum.Analysis,
            maxWidth = 80,
            gridColumnTypeEnum = GridColumnTypeEnum.Number,
         hasExplanation = true,
},
      new CPHColDefDTO {
         headerName = "V. Over Strategy",
            hide = true,
            field = "veryOverStrategyCount",
            shouldTotal = true,
            colId = "veryOverStrategyCount",
            gridColumnSectionEnum = GridColumnSectionEnum.Analysis,
            maxWidth = 80,
            gridColumnTypeEnum = GridColumnTypeEnum.Number,
         hasExplanation = true,
}
   };

         if (options != null && options?.optionShowPhysicalLocation_Col == true)
         {
            columns.Add(new CPHColDefDTO()
            {
               headerName = "Physical Location",
               field = "PhysicalLocation",
               hide = true,
               gridColumnSectionEnum = GridColumnSectionEnum.StockInformation,
               colId = "PhysicalLocation",
               shouldAverageIfValue = false,
               width = 10,
               gridColumnTypeEnum = GridColumnTypeEnum.Label,
               provideExplanation = true
            });
         }

         if (options != null && options?.optionShowDMSSellingPrice_Col == true)
         {
            columns.Add(new CPHColDefDTO()
            {
               headerName = "DMS Selling Price",
               field = "DMSSellingPrice",
               gridColumnSectionEnum = GridColumnSectionEnum.AdvertisedPrice,
               colId = "DMSSellingPrice",
               shouldAverageIfValue = true,
               hide = true,
               width = 10,
               gridColumnTypeEnum = GridColumnTypeEnum.Currency,
               provideExplanation = true
            });
         }

         if (options != null && options?.optionShowVsDMSSellingPrice_Col == true)
         {
            columns.Add(new CPHColDefDTO()
            {
               headerName = "Vs DMS Selling",
               field = "VsDMSSellingPrice",
               gridColumnSectionEnum = GridColumnSectionEnum.AdvertisedPrice,
               colId = "VsDMSSellingPrice",
               shouldAverageIfValue = true,
               hide = true,
               width = 10,
               gridColumnTypeEnum = GridColumnTypeEnum.Currency,
               provideExplanation = true
            });
         }

         if (options != null && options?.optionAllowTestStrategy == true)
         {
            columns.Add(new CPHColDefDTO()
            {
               headerName = "Test Strategy Price",
               hide = true,
               field = "TestStrategyPrice",
               shouldAverageIfValue = true,
               gridColumnSectionEnum = GridColumnSectionEnum.TestStrategy,
               colId = "TestStrategyPrice",
               width = 10,
               gridColumnTypeEnum = GridColumnTypeEnum.Currency,
               provideExplanation = true
            });
            columns.Add(new CPHColDefDTO()
            {
               headerName = "Test Strategy Days To Sell",
               hide = true,
               field = "TestStrategyDaysToSell",
               shouldAverageIfValue = true,
               gridColumnSectionEnum = GridColumnSectionEnum.TestStrategy,
               colId = "TestStrategyDaysToSell",
               width = 10,
               gridColumnTypeEnum = GridColumnTypeEnum.Number,
               provideExplanation = true
            });
            columns.Add(new CPHColDefDTO()
            {
               headerName = "Advertised Vs Test Strategy",
               hide = true,
               shouldAverageIfValue = true,
               gridColumnSectionEnum = GridColumnSectionEnum.TestStrategy,
               colId = "VsTestStrategyPrice",
               field = "VsTestStrategyPrice",
               width = 10,
               gridColumnTypeEnum = GridColumnTypeEnum.CurrencyWithPlusMinus,
               provideExplanation = true
            });
            columns.Add(new CPHColDefDTO()
            {
               headerName = "Vs Test Strategy Band",
               hide = true,
               field = "VsTestStrategyBanding",
               gridValueGetterEnum = ValueGetterEnum.formatTestStrategyBandLabel,
               colId = "VsTestStrategyBanding",
               width = 7,
               gridColumnTypeEnum = GridColumnTypeEnum.LabelSetFilter,
               gridColumnSectionEnum = GridColumnSectionEnum.TestStrategy,
               gridCellRendererEnum = CellRendererEnum.AutoTraderLozengeRenderer,
               cellClass = "agAlignCentre",
               sortable = true,
               gridComparatorEnum = ComparatorEnum.getSortOrderForVsStrategyBanding
            });
            columns.Add(new CPHColDefDTO()
            {
               headerName = "Test Strategy vs Strategy",
               hide = true,
               field = "testStrategyPriceVsStrategyPrice",
               shouldAverageIfValue = true,
               gridColumnSectionEnum = GridColumnSectionEnum.TestStrategy,
               colId = "testStrategyPriceVsStrategyPrice",
               width = 10,
               gridColumnTypeEnum = GridColumnTypeEnum.CurrencyWithPlusMinus,
            });
         }

         return columns;
      }
   }
}