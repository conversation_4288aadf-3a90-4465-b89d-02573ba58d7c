import { Component, Input, OnInit } from '@angular/core';
import { GridApi, GridOptions, GridReadyEvent } from 'ag-grid-community';
import { CphPipe } from 'src/app/cph.pipe';
import { SameModelAdvert } from 'src/app/model/SameModelAdvert';
import { AutoTraderAdvertImage } from 'src/app/_cellRenderers/autoTraderAdvertImage.component';
import { ColumnTypesService } from 'src/app/services/columnTypes.service';
import { AGGridMethodsService } from 'src/app/services/agGridMethods.service';
import { ConstantsService } from 'src/app/services/constants.service';

@Component({
  selector: 'currentStockTable',
  templateUrl: './currentStockTable.component.html',
  styleUrls: ['./currentStockTable.component.scss']
})
export class CurrentStockTableComponent implements OnInit {
  @Input() data: SameModelAdvert[];
  @Input() fullHeight: boolean;
  gridOptions: GridOptions;
  gridApi: Grid<PERSON>pi;
  resizeObserver: ResizeObserver;

  constructor(
    public cphPipe: CphPipe,
    private colTypesService:ColumnTypesService,
    private gridHelpersService: AGGridMethodsService,
    public constants: ConstantsService
  ) { }

  ngOnInit(): void {
    this.setGridDefinitions();

    const table = document.getElementById('currentStockTable');

    this.resizeObserver = new ResizeObserver(entries => {
      if (this.gridApi) {
        setTimeout(() => {
          this.gridApi.sizeColumnsToFit();
        }, 250)
      }
    });

    if (table) {
      this.resizeObserver.observe(table);
    }
  }

  ngOnDestroy(): void {
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
  }

  setGridDefinitions() {
    this.gridOptions = {
      defaultColDef: {
        resizable: true,
        sortable: true,
        filterParams: {
          applyButton: false,
          clearButton: true,
          cellHeight: this.gridHelpersService.getFilterListItemHeight()
        },
        autoHeight: true,
        headerComponentParams: {
          showPinAndRemoveOptions: false
        },
        autoHeaderHeight: true
      },
      columnTypes: {...this.colTypesService.provideColTypes([])} ,
      rowSelection: 'multiple',
      rowData: this.data,
      pinnedTopRowData:this.makePinnedTopRowData(this.data),
      columnDefs: [
        { headerName: 'Photo', colId: 'ImageURLs', minWidth:40, field: 'ImageURL', type: 'special', cellRenderer: AutoTraderAdvertImage, width: 40 },
        { headerName: 'Site', colId: 'RetailerName', field: 'RetailerName', type: 'label', width: 75 },
        { headerName: 'Reg', colId: 'Reg', field: 'Reg', type: 'label', width: 40,minWidth:40 },
        { headerName: 'Days Listed', colId: 'DaysListed', sort:'asc', field: 'DaysListed', type: 'number', width: 30 },
        { headerName: 'Derivative', colId: 'Derivative', field: 'Derivative', type: 'label', width: 100 },
        { headerName: 'Supplied Price', colId: 'AdvertisedPrice', field: 'AdvertisedPrice', type: 'currency', width: 40 },
        { headerName: 'Price Position', colId: 'PricePosition', field: 'PricePosition', type: 'percent1dp', width: 40 },
        { headerName: 'Profit', colId: 'Profit', field: 'Profit', type: 'currency', width: 40 }
      ],
      onGridReady: (event: GridReadyEvent) => this.onGridReady(event),
      getRowHeight: (params) => {
        if (params.node.rowPinned) {
          return this.gridHelpersService.getRowPinnedHeight();
        } else {
          return this.gridHelpersService.getStandardHeight();
        }
      }
    }
  }


    makePinnedTopRowData(data:SameModelAdvert[]):SameModelAdvert[]{
      let newRow = {
        RetailerName:null,
        Derivative: 'Average',
        AdvertisedPrice:0,
        PricePosition:0
      } as SameModelAdvert
      let countNonZeroPrice = 0;
      let countNonZeroPP = 0;
      
      data.forEach(item=>{
        if(item.AdvertisedPrice>0){
          countNonZeroPrice++;
          newRow.AdvertisedPrice += item.AdvertisedPrice;
        }
        if(item.PricePosition>0){
          countNonZeroPP++;
          newRow.PricePosition += item.PricePosition;
        }
      })
      newRow.AdvertisedPrice = newRow.AdvertisedPrice/countNonZeroPrice;
      newRow.PricePosition = newRow.PricePosition/countNonZeroPP;
      return [newRow]
    }

  onGridReady(event: GridReadyEvent) {
    this.gridApi = event.api;
    this.gridApi.sizeColumnsToFit();
  }
}
