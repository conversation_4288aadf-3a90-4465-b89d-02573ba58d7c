export enum GlobalParamKey {  //to add a key just add it below, plus 3 more places below.   Back end and db should not need anything more.
   ageDaysListedFrom = 'ageDaysListedFrom',
   autotraderAdvertsUpdateDate = 'autotraderAdvertsUpdateDate',
   dealLatestsUpdateDate = 'dealLatestsUpdateDate',
   LeavingVehiclesHardStart = 'LeavingVehiclesHardStart',
   stocksUpdateDate = 'stocksUpdateDate',
   ValueVehicleAtAllSites = 'ValueVehicleAtAllSites',
   ShowLCVToggle = 'ShowLCVToggle',

   lastCommissionLockedMonth = 'lastCommissionLockedMonth',
   webAppShowScratchcard = 'webAppShowScratchcard',
   webAppShowSpringIncentive = 'webAppShowSpringIncentive',
   webAppShowDefaultShowUnPublishedVehicles = 'webAppShowDefaultShowUnPublishedVehicles',
   show30DayLeavingSlider = 'show30DayLeavingSlider',
   show30DayRetailerSoldLeavingSlider = 'show30DayRetailerSoldLeavingSlider',
   webAppShowOnlyKeyPriceChanges = 'webAppShowOnlyKeyPriceChanges',
   ShowAgeOwnersColourOnPriceChanges = 'ShowAgeOwnersColourOnPriceChanges',
   UseUserPostcode = 'UseUserPostcode',
   showRetailRatingTrendOnChart = 'showRetailRatingTrendOnChart',
   ShowDidBeatWholesaleTargetColumn = 'ShowDidBeatWholesaleTargetColumn',
   ShowBulkPriceChangeMenu = 'ShowBulkPriceChangeMenu',
   ShowManualPriceChangeOption = 'ShowManualPriceChangeOption',
   showPerformanceRatingOnDailyEmail = 'showPerformanceRatingOnDailyEmail',
   requireSpecificPageAccess = 'requireSpecificPageAccess',
   protectPrepCosts = 'ProtectPrepCosts',
   ShowDaysInStockByDefault = 'ShowDaysInStockByDefault',
}

//used at compile time
export type GlobalParamTypeMap = {
   //usable types are: string,int,string[],int[],Json,number
   [GlobalParamKey.ageDaysListedFrom]: string; //ensure you declare the type accurately.   This will force you to create a method below of converting it to / from a string for the db.
   [GlobalParamKey.autotraderAdvertsUpdateDate]: Date;
   [GlobalParamKey.dealLatestsUpdateDate]: Date;
   [GlobalParamKey.LeavingVehiclesHardStart]: Date;
   [GlobalParamKey.stocksUpdateDate]: Date;
   [GlobalParamKey.ValueVehicleAtAllSites]: boolean;
   [GlobalParamKey.ShowLCVToggle]: boolean;
   [GlobalParamKey.lastCommissionLockedMonth]: Date;
   [GlobalParamKey.webAppShowScratchcard]: boolean;
   [GlobalParamKey.webAppShowSpringIncentive]: boolean;
   [GlobalParamKey.webAppShowDefaultShowUnPublishedVehicles]: boolean;
   [GlobalParamKey.show30DayLeavingSlider]: boolean;
   [GlobalParamKey.show30DayRetailerSoldLeavingSlider]: boolean;
   [GlobalParamKey.webAppShowOnlyKeyPriceChanges]: boolean;
   [GlobalParamKey.ShowAgeOwnersColourOnPriceChanges]: boolean;
   [GlobalParamKey.UseUserPostcode]: boolean;
   [GlobalParamKey.showRetailRatingTrendOnChart]: boolean;
   [GlobalParamKey.ShowDidBeatWholesaleTargetColumn]: boolean;
   [GlobalParamKey.ShowBulkPriceChangeMenu]: boolean;
   [GlobalParamKey.ShowManualPriceChangeOption]: boolean;
   [GlobalParamKey.showPerformanceRatingOnDailyEmail]: boolean;
   [GlobalParamKey.requireSpecificPageAccess]: boolean;
   [GlobalParamKey.protectPrepCosts]: boolean;
   [GlobalParamKey.ShowDaysInStockByDefault]: boolean;

};

//these 2 used at run time
type GlobalParamType = 'string' | 'stringArray' | 'boolean' | 'date';
export const PreferenceTypeMeta: { [key in GlobalParamKey]: GlobalParamType } = {
   [GlobalParamKey.ageDaysListedFrom]: 'string',
   [GlobalParamKey.autotraderAdvertsUpdateDate]: 'date',
   [GlobalParamKey.dealLatestsUpdateDate]: 'date',
   [GlobalParamKey.LeavingVehiclesHardStart]: 'date',
   [GlobalParamKey.stocksUpdateDate]: 'date',
   [GlobalParamKey.ValueVehicleAtAllSites]: 'boolean',
   [GlobalParamKey.ShowLCVToggle]: 'boolean',
   [GlobalParamKey.lastCommissionLockedMonth]: 'date',
   [GlobalParamKey.webAppShowScratchcard]: 'boolean',
   [GlobalParamKey.webAppShowSpringIncentive]: 'boolean',
   [GlobalParamKey.webAppShowDefaultShowUnPublishedVehicles]: 'boolean',
   [GlobalParamKey.show30DayLeavingSlider]: 'boolean',
   [GlobalParamKey.show30DayRetailerSoldLeavingSlider]: 'boolean',
   [GlobalParamKey.webAppShowOnlyKeyPriceChanges]: 'boolean',
   [GlobalParamKey.ShowAgeOwnersColourOnPriceChanges]: 'boolean',
   [GlobalParamKey.UseUserPostcode]: 'boolean',
   [GlobalParamKey.showRetailRatingTrendOnChart]: 'boolean',
   [GlobalParamKey.ShowDidBeatWholesaleTargetColumn]: 'boolean',
   [GlobalParamKey.ShowBulkPriceChangeMenu]: 'boolean',
   [GlobalParamKey.ShowManualPriceChangeOption]: 'boolean',
   [GlobalParamKey.showPerformanceRatingOnDailyEmail]: 'boolean',
   [GlobalParamKey.requireSpecificPageAccess]: 'boolean',
   [GlobalParamKey.protectPrepCosts]: 'boolean',
   [GlobalParamKey.ShowDaysInStockByDefault]: 'boolean',
};


/*
  And here is the SQL to create one in the back end:
  INSERT INTO GlobalParams (Description,textValue,value,DealerGroup_Id)
  values
  ('showRetailRatingTrendOnChart','True',0,28)


*/


export class GlobalParam {

   Id: number;
   Description: string;
   DateFrom: Date;
   DateTo: Date;
   Value: number;
   TextValue: string;


   constructor(pref?: GlobalParam) {
      if (pref) {

         this.Description = pref.Description;
         this.DateFrom = pref.DateFrom;
         this.DateTo = pref.DateTo;
         this.Value = pref.Value;
         this.TextValue = pref.TextValue;
      }

   }


   public getGlobalParam<globalPrefKey extends GlobalParamKey>(key: globalPrefKey): GlobalParamTypeMap[globalPrefKey] {
      const type = PreferenceTypeMeta[key];

      // if (type === 'stringArray') {
      //   return this.TextValue.split(',') as GlobalParamTypeMap[globalPrefKey];
      // }
      // else
      if (type === 'boolean') {
         return (this.TextValue === 'True') as GlobalParamTypeMap[globalPrefKey]
      } else if (type === 'string') {
         return this.TextValue as GlobalParamTypeMap[globalPrefKey]
      } else if (type === 'date') {
         return new Date(this.DateFrom) as GlobalParamTypeMap[globalPrefKey]
      }
   }


// New method to set the parameter value based on type
   public setGlobalParam<globalPrefKey extends GlobalParamKey>(key: globalPrefKey, value: GlobalParamTypeMap[globalPrefKey]): void {
      const type = PreferenceTypeMeta[key];

      if (type === 'boolean' && typeof value === 'boolean') {
         // Store as "True" or "False" in TextValue
         this.TextValue = value ? 'True' : 'False';
      } else if (type === 'string' && typeof value === 'string') {
         // Directly store the string in TextValue
         this.TextValue = value;
      } else if (type === 'date' && value instanceof Date) {
         // Store the Date in DateFrom (and DateTo if relevant)
         this.DateFrom = value;
      } else {
         throw new Error(`Type mismatch: Cannot set key ${key} with value of type ${typeof value}`);
      }
   }

}
