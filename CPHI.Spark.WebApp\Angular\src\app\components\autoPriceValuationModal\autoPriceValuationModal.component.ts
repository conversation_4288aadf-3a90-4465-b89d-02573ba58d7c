import {ChangeDetectorRef, Component, OnInit} from '@angular/core';
import {NgbModal, NgbModalRef} from '@ng-bootstrap/ng-bootstrap';
import {Subscription} from 'rxjs';
import {debounceTime, groupBy, mergeMap} from 'rxjs/operators';
import {DayToSellAndPriceIndicator} from "src/app/model/DayToSellAndPriceIndicator";
import {LocationAndStrategyPrice} from 'src/app/model/LocationAndStrategyPrice.model';
import {RetailRatingParams} from 'src/app/model/RetailRatingParams';
import {PreferenceKey} from 'src/app/model/UserPreference';
import {ValuationPriceSet} from 'src/app/model/ValuationPriceSet.model';
import {ValuationResultForNewVehicleToSave} from 'src/app/model/ValuationResultForNewVehicleToSave.model';

import {VehicleSpecOption} from 'src/app/model/VehicleSpecOption.model';
import {
   GetEstimatedDayToSellAndPriceIndicatorParams
} from 'src/app/pages/performanceTrends/GetEstimatedDayToSellAndPriceIndicatorParams';
import {ConstantsService} from 'src/app/services/constants.service';
import {GetDataMethodsService} from 'src/app/services/getDataMethods.service';
import {SelectionsService} from 'src/app/services/selections.service';
import {ValuationCostingFieldEnum} from './valuationCosting/ValuationCostingDTO';
import {ValuationModalParams} from './valuationCosting/ValuationModalParams';
import {ValuationCosting} from './valuationCosting/valuationCosting';
import {newValue, VehicleValuationService} from './vehicleValuation.service';
import {StrategyPriceBuildUp} from 'src/app/model/StrategyPriceBuildUp';
import {PrintService} from 'src/app/services/print.service';


@Component({
   selector: 'autoPriceValuationModal',
   templateUrl: './autoPriceValuationModal.component.html',
   styleUrls: ['./autoPriceValuationModal.component.scss']
})
export class AutoPriceValuationModalComponent implements OnInit {
   openPanels: string[];
   //vehicleConditions: string[] = ['Poor', 'Fair', 'Good', 'Great', 'Excellent'];
   valCostingNewValueSub: Subscription;
   initialDataLoadedSub: Subscription;

   constructor(
      public constantsService: ConstantsService,
      public selectionsService: SelectionsService,
      public getDataService: GetDataMethodsService,
      public modalService: NgbModal,
      public service: VehicleValuationService,
      private printService: PrintService,
      private cdr: ChangeDetectorRef
   ) {
   }


   get userHasMultiSite() {
      return this.selectionsService.userSiteIds.length > 1;
   }

   ngOnInit() {

      this.valCostingNewValueSub = this.service.valuationCostingChangeEmitter.pipe(
         groupBy((val: newValue) => val.field),  // Group by fieldName
         mergeMap(group => group.pipe(
            debounceTime(600)  // Apply debounce to each group individually
         ))
      ).subscribe(res => {
         this.onNewCostingsValue(res);
         this.buildTheBuildUpLayers();
      });

      // Subscribe to initial data loaded event
      this.initialDataLoadedSub = this.service.initialDataLoadedEmitter.subscribe(() => {
         this.buildTheBuildUpLayers();
      });
   }


   ngOnDestroy() {
      this.service.optionsModalRef = null;
      this.service.strategyPriceBySiteModalRef = null;
      this.service.optionsTableRef = null;
      this.service.optionsGridApi = null;

      if (this.valCostingNewValueSub) {
         this.valCostingNewValueSub.unsubscribe()
      }

      if (this.initialDataLoadedSub) {
         this.initialDataLoadedSub.unsubscribe()
      }

   }


   initParams(params: ValuationModalParams, modalRef: NgbModalRef) {

      this.service.valuationModalResultNew = null;
      this.openPanels = this.service.userPrefsService.getPreference(PreferenceKey.ValuationModalOpenPanels) ?? [];

      //if we have params
      if (params) {
         this.service.vehicleReg = params.reg;
         this.service.mileage = params.mileage;
         this.service.vehicleCondition = params.condition;
         this.service.valuationId = params.valuationId;
         this.service.colour = params.colour;
      }


      //this.service.strategyBySite = [];
      this.service.valuationModalResultNew = null;
      this.service.optionsModalOptions = null;
      this.service.optionsModalValuation = null;

      this.service.valuationCosting = new ValuationCosting(this.service);
      this.service.chosenRetailerSite = null;

      this.service.builtUpLayers = null;


      this.service.getVehicleInformationAndValuation(modalRef);
      //this.service.getVehicleInformationAndValuation();
   }


   onNewCostingsValue(val: newValue) {
      const fieldName: string = ValuationCostingFieldEnum[val.field].toString();
      this.service.valuationCosting[fieldName] = val.value
      //console.log('costing value changed:', val)

      if (val.field === ValuationCostingFieldEnum.Sales) {
         //we have changed the selling price
         const params = this.service.updateCompetitorAnalysisForOurNewPrice();
         this.service.competitorAnalysisService.dealWithNewParams(params)
         this.getEstimatedDayToSellAndPriceIndicator(this.service.valuationCosting.sales)
      }

      if (val.field == ValuationCostingFieldEnum.PricePosition) {
         //get new days to sell and price indicator
         setTimeout(() => {
            const params = this.service.updateCompetitorAnalysisForOurNewPrice();
            this.service.competitorAnalysisService.dealWithNewParams(params)
            this.getEstimatedDayToSellAndPriceIndicator(this.service.valuationCosting.sales)
         }, 100)

      }
   }


   getEstimatedDayToSellAndPriceIndicator(newPrice: number) {

      const newPricePosition = newPrice / this.service.valuationModalResultNew.ValuationPriceSet.RetailThisVehicle;

      if (newPricePosition > 1.1 || newPricePosition < 0.9) {
         this.dealWithUnavailablePriceIndicator()
         return;
      }


      const parms: GetEstimatedDayToSellAndPriceIndicatorParams = {
         AdvertiserIds: this.service.valuationModalResultNew.LocationStrategyPrices.map(x => x.RetailerSite.RetailerId),
         DerivativeId: this.service.valuationModalResultNew.VehicleInformation.DerivativeId,
         FirstRegisteredDate: this.service.valuationModalResultNew.VehicleInformation.FirstRegistered,
         Mileage: this.service.valuationModalResultNew.VehicleInformation.Mileage,
         StrategyPrice: Math.round(newPrice),
         VehicleHasOptionsSpecified: !!this.service.valuationModalResultNew.VehicleInformation.ChosenOptions && this.service.valuationModalResultNew.VehicleInformation.ChosenOptions.length > 0,
         VehicleAdvertPortalOptions: this.service.valuationModalResultNew.VehicleInformation.ChosenOptions,

         AverageValuation: this.service.valuationModalResultNew.ValuationPriceSet.RetailAverageSpec,
         AdjustedValuation: this.service.valuationModalResultNew.ValuationPriceSet.RetailThisVehicle
      }

      this.service.amCalculating = true;
      this.getDataService.getAutoPriceEstimatedDayToSellAndPriceIndicator(parms)
         .subscribe((res: DayToSellAndPriceIndicator) => {
            this.service.amCalculating = false;
            if (res.PriceIndicator) {

               if (res.DaysToSellResults.length > 0) {
                  this.service.chosenVehicleLocationStrategyPriceBuild.DaysToSell = res.DaysToSellResults.find(x => x.RetailerSiteRetailerId === this.service.chosenRetailerSite.RetailerId).DaysToSell;
               }

               //update all the days to sell results for each site
               res.DaysToSellResults.forEach(res => {
                  const existingLocationStrategyPrice = this.service.valuationModalResultNew.LocationStrategyPrices.find(x => x.RetailerSite.RetailerId === res.RetailerSiteRetailerId);
                  existingLocationStrategyPrice.DaysToSell = res.DaysToSell
               })
               //update price indicator
               this.service.valuationModalResultNew.ValuationPriceSet.PriceIndicator = res.PriceIndicator;
               //tell the trended valuation thing to refresh
               if (this.service.trendedValuationInstance) {
                  this.service.trendedValuationInstance.dealWithNewData(this.service.valuationModalResultNew.TrendedValuations)
               }
            } else {
               this.dealWithUnavailablePriceIndicator()
            }


         }, error => {
            this.service.amCalculating = false;
            console.error('Failed to retrieve estimated Day to Sell & Price Indicator', error);
         })
   }

   dealWithUnavailablePriceIndicator() {
      this.service.constantsService.toastSuccess("Not possible to work out")
      this.service.valuationModalResultNew.ValuationPriceSet.PriceIndicator = "Unknown";
      this.service.chosenVehicleLocationStrategyPriceBuild.DaysToSell = 0;
      this.service.valuationModalResultNew.LocationStrategyPrices.map(x => x.DaysToSell = 0);
      if (this.service.trendedValuationInstance) {
         this.service.trendedValuationInstance.dealWithNewData(this.service.valuationModalResultNew.TrendedValuations)
      }
   }


   // async getValuationForAdvert() {
   //   this.service.batchId ? await this.getExistingValuationForAdvert(this.service.batchId) : await this.service.getNewValuationForAdvert();
   // }

   async getExistingValuationForAdvert(batchId: number) {
      await this.getDataService.getExistingValuationForAdvert(batchId).then(async (valuation: ValuationPriceSet) => {
         //wait this.service.onValuationLoad(valuation);
      }, error => {
         console.error('Error returning valuation price set');
         this.selectionsService.triggerSpinner.emit({show: false});
      })
   }


   saveValuation() {

      this.selectionsService.triggerSpinner.emit({show: true, message: 'Saving...'});

      const vehicleSpecOptions: VehicleSpecOption[] = [];
      if (this.service.valuationModalResultNew.VehicleInformation.ChosenOptions) {
         this.service.valuationModalResultNew.VehicleInformation.ChosenOptions.forEach(option => {
            const fullOption: VehicleSpecOption = new VehicleSpecOption(this.service.valuationModalResultNew.SpecOptions.find(x => x.Name == option));
            fullOption.IsChosen = true;
            vehicleSpecOptions.push(fullOption);
         })
      }

      const params: ValuationResultForNewVehicleToSave = {
         ValuationId: this.service.valuationId,
         VehicleReg: this.service.vehicleReg.toUpperCase(),
         FirstRegistered: new Date(this.service.valuationModalResultNew.VehicleInformation.FirstRegistered),
         Mileage: this.service.mileage == null ? 0 : this.service.mileage,
         Condition: this.service.vehicleCondition,
         DerivativeId: this.service.valuationModalResultNew.VehicleInformation.DerivativeId,
         RetailRating: this.service.chosenVehicleLocationStrategyPriceBuild.RetailRating,
         Valuation: this.service.valuationModalResultNew.ValuationPriceSet,
         VehicleSpecOptions: vehicleSpecOptions,
         SitesStrategy: this.service.valuationModalResultNew.LocationStrategyPrices.map(x => new LocationAndStrategyPrice(x)),
         PrepCost: 0,//this.showNormalPrepCost,
         ExtraPrepRows: [],
         Costing: this.service.valuationCosting.getState(),
         Colour: this.service.colour,
         SpecificColour: this.service.colour,
         Notes: this.service.notes,
      }

      this.getDataService.saveVehicleValuation(params).subscribe(() => {
         this.constantsService.toastSuccess('Saved valuation');
         //this.service.batchId = res;
         this.selectionsService.triggerSpinner.emit({show: false});
      }, error => {
         console.error('Error saving valuation');
         this.constantsService.toastDanger('Failed to save valuation');
         this.selectionsService.triggerSpinner.emit({show: false});
      })
   }

   closeModal() {
      this.modalService.dismissAll();
   }


   updateCondition(condition: string) {
      this.service.vehicleCondition = condition;
      //this.service.batchId = null;
      this.service.valuationId = null;
      this.service.getVehicleInformationAndValuation(null);
   }

   getRetailRatingParams(): RetailRatingParams {
      return {
         Make: this.service.valuationModalResultNew?.VehicleInformation.Make,
         RetailRating: this.service.valuationModalResultNew.LocationStrategyPrices.find(x => x.RetailerSite.Id == this.service.chosenRetailerSite.Id)?.RetailRating,
         NationalRetailRating: this.service.valuationModalResultNew?.VehicleInformation.NationalRetailRating
      }
   }


   buildTheBuildUpLayers() {
      this.service.builtUpLayers = {
         buildUpData: this.service.chosenVehicleLocationStrategyPriceBuild?.BuildUpItems.map(x => new StrategyPriceBuildUp(x)),
         retailRating: this.service.chosenVehicleLocationStrategyPriceBuild?.RetailRating,
         daysToSell: this.service.chosenVehicleLocationStrategyPriceBuild?.DaysToSell,
         daysBookedIn: 0,
         daysListed: 0,
         daysInStock: 0,
         make: this.service.valuationModalResultNew.VehicleInformation.Make,
         colour: this.service.valuationModalResultNew.VehicleInformation.Colour,
         specificColour: this.service.valuationModalResultNew.VehicleInformation.SpecificColour,
         ageAndOwners: this.service.valuationModalResultNew.VehicleInformation.AgeAndOwners,
         odometer: this.service.valuationModalResultNew.VehicleInformation.Mileage,
         performanceRating: null,
         fuelType: this.service.valuationModalResultNew.VehicleInformation.FuelType || '',
         valueBand: this.determineValueBand(this.service.chosenVehicleLocationStrategyPriceBuild?.StrategyPrice || 0),
         regYear: this.service.valuationModalResultNew.VehicleInformation.FirstRegistered ? new Date(this.service.valuationModalResultNew.VehicleInformation.FirstRegistered).getFullYear().toString() : new Date().getFullYear().toString(),
         liveMarketCondition: '0%', // TODO: Get actual live market condition from data source
         dateRange: '',
      }

   }


   onPanelChange(event: any, panelId: string): void {
      const panelActive = event.nextState;

      if (panelActive) {
         // Panel is being opened
         if (!this.openPanels.includes(panelId)) {
            this.openPanels.push(panelId);
         }
      } else {
         // Panel is being closed
         this.openPanels = this.openPanels.filter(id => id !== panelId);
      }

      this.service.userPrefsService.setPreference(PreferenceKey.ValuationModalOpenPanels, this.openPanels);

   }


   stockCover() {
      return Math.round(this.service.valuationModalResultNew.StockLevelAndCover.find(x => x.IsTotal).Cover);
   }


   soldLast6mMessage() {
      const recentlySold = this.service.valuationModalResultNew.RecentlySoldThisModel;
      if (recentlySold.length == 0) {
         return ''
      }
      const vehicleCount = this.service.constantsService.pluralise(recentlySold.length, 'vehicle', 'vehicles');
      const daysListed = this.constantsService.sum(recentlySold.map(x => x.DaysListed)) / recentlySold.length;
      const finalPP = this.constantsService.sum(recentlySold.map(x => x.LastPP)) / recentlySold.length;

      return `${vehicleCount}, average days listed: ${this.service.cphPipe.transform(daysListed, 'number', 0)}, final price position: ${this.service.cphPipe.transform(finalPP, 'percent', 1)}`
   }

   private determineValueBand(price: number): string {
      if (price < 5000) return "£5k-£10k";
      if (price < 10000) return "£5k-£10k";
      if (price < 15000) return "£10k-£15k";
      if (price < 20000) return "£15k-£20k";
      if (price < 30000) return "£20k-£30k";
      if (price < 40000) return "£30k-£40k";
      if (price < 50000) return "£40k-£50k";
      return ">£50k";
   }

   vehicleDetailsString() {
      if (!this.service.valuationModalResultNew) {
         return ''
      }

      if (this.service.valuationModalResultNew.ErrorMessage) {
         return 'Error: Unable to value vehicle';
      }

      const val = this.service.valuationModalResultNew;

      return ` | ${val.VehicleInformation.VehicleReg} | ${val.VehicleInformation.Make} ${val.VehicleInformation.Model} ${val.VehicleInformation.Derivative} | ${this.service.constantsService.pluralise(val.VehicleInformation.Mileage, 'mile', 'miles')} `
   }

   async printValuation() {

      const fileName = `Vehicle Valuation - ${this.service.valuationModalResultNew.VehicleInformation.VehicleReg}`;

      try {
         // Store original accordion state
         const originalOpenPanels = [...this.openPanels];

         const accordions = [
            'vehicleDetails', 'saleDetails', 'vehicleHistory', 'valuationAndMetrics',
            'sellingProspects', 'recentlySold', 'currentStock', 'stockCover',
            'eachSiteValuation', 'prepCosts'
         ];

         const elementsToExpand = [
            '#modalContentContainer', // This has a max-height we need to overwrite
         ];

         // Expand all accordions
         this.openPanels = [...new Set([...this.openPanels, ...accordions])];
         this.cdr.detectChanges();

         // Call the simplified print service
         await this.printService.printElementAsPDF('.modal', fileName, elementsToExpand);

         // Restore original accordion state
         this.openPanels = originalOpenPanels;
         this.cdr.detectChanges();

         this.constantsService.toastSuccess('PDF saved');
      } catch (error) {
         this.constantsService.toastDanger('Failed to generate PDF');
      }
   }
}
