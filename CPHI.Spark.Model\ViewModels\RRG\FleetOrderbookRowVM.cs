﻿using System;

namespace CPHI.Spark.Model.ViewModels.RRG

{

    public class FleetOrderbookRowVM
    {
        public int RenaultOrNissanOrderItemId { get; set; }
        public string Chassis { get; set; }
        public string VehicleOrderNumber { get; set; }
        public int OwningMainDealer { get; set; }
        public string Customer { get; set; }
        public string Status { get; set; }
        public string Model { get; set; }
        public string Version { get; set; }
        public int SalesExecPin { get; set; }
        public string Colour { get; set; }
        public string Options { get; set; }
        public string LabelOptions { get; set; }
        public string SalesChannel { get; set; }
        public string LeaseNumber { get; set; }
        public string Comments { get; set; }
        
        public string EbbonStatus { get; set; }
        public string EbbonStage { get; set; }
        public DateTime? OrderDate { get; set; }
        public DateTime? ActualMatchedDate { get; set; }
        public DateTime? FRD { get; set; }
        public DateTime? PromAtic { get; set; }
        public DateTime? ProbableAtic { get; set; }
        public DateTime? ActualAtic { get; set; }
        public DateTime? DDDDate { get; set; }
        public DateTime? RegistrationDate { get; set; }
        public string IsMatchedInMonth {
            get
            {
                int yr = DateTime.Now.Year;
                int mth = DateTime.Now.Month;
                return ActualMatchedDate != null && ((DateTime)ActualMatchedDate).Year == yr && ((DateTime)ActualMatchedDate).Month == mth ? "true" : "false"; 
            }
        }
        public string IsInvoicedInMonth
        {
            get
            {
                int yr = DateTime.Now.Year;
                int mth = DateTime.Now.Month;
                return InvoiceDateToCustomer != null && ((DateTime)InvoiceDateToCustomer).Year == yr && ((DateTime)InvoiceDateToCustomer).Month == mth ? "true" : "false";
            }
        }
        public string IsRegisteredInMonth
        {
            get
            {
                int yr = DateTime.Now.Year;
                int mth = DateTime.Now.Month;
                return RegistrationDate != null && ((DateTime)RegistrationDate).Year == yr && ((DateTime)RegistrationDate).Month == mth ? "true" : "false";
            }
        }
        public string CustomerType { get; set; }
        public int? TrueAge { get; set; }
        public int AgeAtEom { get; set; }
        public string DeliveryAccount { get; set; }
        public string Brand { get; set; } // Nissan / Renault

        public string FonName { get; set; }
        public string FonData { get; set; }

        public string AlarmData { get; set; }
        public string StockCategory { get; set; }

        public string BcaStatus { get; set; }
        public DateTime? BcaArrivalDate { get; set; }
        public DateTime? BcaDdd { get; set; }
        public DateTime? BcaEstArrivalDate { get; set; }
        public int? Campaign { get; set; }
        public int? Damage { get; set; }
        public int? Estimate { get; set; }
        public int? Mechanical { get; set; }
        public string SparkComments { get; set; }

        public string StockNumber { get; set; }
        public decimal TotalNLProfit { get; set; }
        public DateTime? InvoiceDateToCustomer { get; set; }
        public bool IsInvoiced { get; set; }
        public int? GFCStockAge { get; set; }

        private string _reg; // To prevent recursive stack overflow exception
        public string Reg
        {
            get => _reg ?? DealsReg;
            set => _reg = value;
        }

        public string DealsReg { get; set; }
        public string ReportReg { get; set; }
        public DateTime? DeliveryDate { get; set; }
        public string DeliveryMonth { get; set; }
        public string DeliveryAgent { get; set; }
        public bool IsHidden { get; set; }
        public bool IsRemoved { get => RemovedDate != null; }
        public DateTime? RemovedDate { get; set; }

        //extras
        public string Comment { get; set; }
        public string OrderComments { get; set; }
        public string DropCode { get; set; }
        public string EndUser { get; set; }
        public string FonCode { get; set; }
        public string FonDescription { get; set; }
        public string CustomerOrderNo { get; set; }
        public string DeliveryDepot { get; set; }
        public bool DriverPackRequired { get; set; }
        public bool DriverPackOrdered { get; set; }
        public string PivgReference { get; set; }
        public DateTime? PivgCreationDate { get; set; }
        public DateTime? PivgExpiryDate { get; set; }
        public string InventoryType { get; set; }
        public string SalesPerson { get; set; }
        public string CarPersonalisationAgeEoM { get; set; }
        public string FullConversionAgeEoM { get; set; }
        public string AgeBandAtEom { get; set; }  //a
        //Ren extras
        public string AgeingProfile { get; set; }
        public bool IsOnLatestDownload { get; set; }

        // For Ply Report
        public DateTime? PlyOrderDate { get; set; }
        public DateTime? PlyFitmentDate { get; set; }
        public string PlyComplDate { get; set; }
        public string PlyAccName { get; set; }
        public decimal PlyDealerCost { get; set; }

        public string AccountHandlerName { get; set; }
        public DateTime? ConvBldDte { get; set; }
        public DateTime? ConvEstComplDte { get; set; }
        public string ConverterName { get; set; }
        public DateTime? ConvBldStartDte { get; set; }

        //SPK-4313 4 new fields
        public string RUKIsGoing { get; set; }
        public string RUKForecastReason { get; set; }
        public string RUKForecastSubReason { get; set; }
        public DateTime? RUKForecastFRD { get; set; }

    }

    
}
