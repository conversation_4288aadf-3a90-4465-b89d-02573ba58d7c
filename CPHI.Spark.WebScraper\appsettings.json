{
   //-------------------------------------------------x
   // THIS IS WEB SCRAPER
   //-------------------------------------------------x
  "ConnectionStrings": {
    "RRGUK": "Server=tcp:cphi.database.windows.net,1433; Initial Catalog=sparkRRG;  Persist Security Info=True;User ID=SparkRRGUser; Password=****************; MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;",
      "Vindis": "Server=tcp:cphi.database.windows.net,1433; Initial Catalog=sparkVindis;  Persist Security Info=True;User ID=SparkVindisUser; Password=***************; MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=90;",
    "RRGSpain": "Server=tcp:cphi.database.windows.net,1433; Initial Catalog=sparkRRGSpain; Persist Security Info=true; User ID=sparkSpainUser; Password=****************; MultipleActiveResultSets=False; Encrypt=True; TrustServerCertificate=False; Connection Timeout=30;",
    "AutoPrice": "Server=tcp:cphi.database.windows.net,1433; Initial Catalog=sparkAutoPrice;  Persist Security Info=True;User ID=SparkAutoPriceUser; Password=****************; MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;",
    "Sytner": "Server=tcp:cphi.database.windows.net,1433; Initial Catalog=sparkSytner;  Persist Security Info=True;User ID=SparkSytnerUser; Password=****************; MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=180;"
  },

   "Logging": {
      "LogLevel": {
         "Default": "Information",
         "Microsoft": "Warning",
         "Microsoft.Hosting.Lifetime": "Information"
      }
   },
   "AllowedHosts": "*",
   "EmailSettings": {

      "mailAppTenantId": "7def64f8-8b3f-400a-8ad0-e50eb7e77eef",
      "mailAppId": "9a44d66f-cfe2-4d19-b129-62b3e0dd28aa",
      "mailSecretValue": "****************************************",
      "mailAccount": "<EMAIL>"
   },

   "AppSettings": {
      "triggerJobSenders": "<EMAIL>,<EMAIL>",

      //-------------------------------------------------x
      // For testing using this to force run this job now.  Or null.
      //-------------------------------------------------x
      "forceRunThisJobNowOnly": null, //put the job name in here if you wish to force run it  e.g. "PeopleJob".   Or null (without quotes).  Only ever change appsettings.json not the underlying rrgDev file


      //-------------------------------------------------x
      // Common
      //-------------------------------------------------x
      "serviceName": "CPHI.Spark.WebScraper",
      "fileDestination": "c:\\cphiRoot\\{destinationFolder}\\inbound\\",
      //"fileDestinationDev": "c:\\cphiRoot\\{destinationFolder}Dev\\inbound\\",
      "fileDownloadLocation": "c:\\cphiRoot\\downloads",
      "sendEmailsTo": "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>",


      //-------------------------------------------------x
      // RRG
      //-------------------------------------------------x
      "RRGSiteStockSchedule": "RUN AT 05:00:00",
      "RRGVocSchedule": "DO NOT RUN", // See http://www.cronmaker.com/;jsessionid=node01sik34gwl2vx3ezhdm1rnrgx736310.node0?0


      //-------------------------------------------------x
      // Spain
      //-------------------------------------------------x
      "AlcopaPassword": "q4bi3WrZE53aE5Y",
      "OrdersMadridPassword": "xjgoy&H5Y9@XcGYd", // PKHRZ74X2JVYEmT5 --
      "OrdersValenciaPassword": "YscdbC#oT&tt9AKf", // DFXAL38T7QMYRiN2
      "SpainEserpubliSchedule": "RUN AT 04:30:00",
      "SpainOrdersSchedule": "CUSTOM 0 0/30 6-19 * * ?", // Ratio
      "SpainAlcopaSchedule": "CUSTOM 0 15 10-19 * * ?",


      //-------------------------------------------------x
      // Spain Physical
      //-------------------------------------------------x
      "fileDestinationProcessed": "",
      "DistrinetSchedule": "DO NOT RUN",
      "ftpHostname": "",
      "ftpUsername": "",
      "ftpPassword": "",

      // Brindley NetDirector
      "NetDirectorPassword": "Daytona2026#",
      "NetDirectorSchedule": "DO NOT RUN", //CUSTOM 0 2/5 8-18 * * ? run every five minutes during the hours of 8 AM to 6 PM at 2nd minute


      //-------------------------------------------------x
      // FOR PINEWOOD (ONLY ON VINDIS VM)
      //-------------------------------------------------x
      "BrindleyPinewoodSchedule": "DO NOT RUN", //CUSTOM 0 3/5 8-18 * * ? run every five minutes during the hours of 8 AM to 6 PM at 3rd minute
      "MantlesPinewoodSchedule": "DO NOT RUN", //CUSTOM 0 4/5 8-18 * * ? run every five minutes during the hours of 8 AM to 6 PM at 4th minute

      //-------------------------------------------------x
      // Vindis
      //-------------------------------------------------x
      "auditScrapePassword": "superPass4567@",
      "enquiryMAXPassword": "Blue_Sky99Run!", // St@rrySk!es#34

      "ModixPassword": "BlueElephantRuns42!", // T8gkV2rZqLmP,
      "ModixUsername": "<EMAIL>",
      "ModixRetailPriceReduction": "99",
      "ModixOfferPriceReduction": "1",

      "SalesmasterPassword": "SunnyDay#92go",
      "SalesmasterUsername": "<EMAIL>",

      "VindisDealsIntradaySchedule": "CUSTOM 0 0 9-19 * * ?",
      "VindisDealsDailySchedule": "CUSTOM 0 30 19 * * ?",
      "VindisActivitySchedule": "CUSTOM 0 30 9-18 * * ?",
      "VindisGDPRSchedule": "CUSTOM 0 35 9-18 * * ?",

      "VindisModixSchedule": "CUSTOM 0 1/5 8-18 * * ?", // run every five minutes during the hours of 8 AM to 6 PM at 1st minute
      "SalesmasterSchedule": "CUSTOM 0 2/5 8-18 * * ?", //CUSTOM 0 2/5 8-18 * * ? run every five minutes during the hours of 8 AM to 6 PM at 2nd minute

      "EnquiriesScrapeJob": "CUSTOM 0 40 9-18 * * ?",
      "VindisAuditSchedule": "CUSTOM 0 45 9-18 * * ?",

      // ClickDealer settings - change these as required
      "ClickDealerSchedule": "DO NOT RUN", //CUSTOM 0 0/5 8-18 * * ?  run every five minutes during the hours of 8 AM to 6 PM on the dot
      "ClickDealerPassword": "Spark123!",
      "ClickDealerAutoTraderFee": "120",

      // Dragon Report settings
      "DragonReportSchedule": "CUSTOM 0 0 6 ? * MON-SUN", // run once at 6am every day 
      "DragonReportPin": "36615",
      "DragonReportUsername": "John",
      "DragonReportPassword": "Spark1234",

      "GenericUpdatePriceCheckerSchedule": "CUSTOM 0 29/30 8-19 * * ?" //CUSTOM 0 0/30 8-19 * * ? run every 30 minutes during the hours of 8 AM to 7 PM on 29th minute
   },
   "Monitor": {
      "AddLogMessageURL": "https://cphimonitorapi.azurewebsites.net/api/monitor/",
      "AppKey": "2"
   },
  "AutotraderSettings": {
    "ApiKey": "CPHInsight-PricingBI-22-07-24", //"CPHInsightLtd-PricingBI-SB-21-06-23" ,
    "ApiSecret": "nyGQrMe4oUVTM7iGPZdgghrDP8nTeZKw", // "whlRByHnySVdOMVST1OzITUIU50AlCRr" ,
    "BaseURL": "https://api.autotrader.co.uk", // "https://api-sandbox.autotrader.co.uk"
    "BaseURLForChangingPrices": "https://api.autotrader.co.uk"
  }



}