import {Injectable} from '@angular/core';
import {GlobalParam, GlobalParamKey, GlobalParamTypeMap, PreferenceTypeMeta} from '../model/GlobalParam';

@Injectable({providedIn: 'root'})

export class GlobalParamsService {

   globalParamStore: { [key in GlobalParamKey]?: GlobalParam } = {};

   constructor() {
   }

   getGlobalParam<prefKey extends GlobalParamKey>(key: GlobalParamKey): GlobalParamTypeMap[GlobalParamKey] | undefined {

      if (!this.globalParamStore) {
         return undefined;
      }
      const globalParam: GlobalParam = this.globalParamStore[key];
      if (!globalParam) {
         return undefined;
      }
      return globalParam.getGlobalParam(key) as GlobalParamTypeMap[GlobalParamKey];
   }

   getTypedGlobalParam<K extends GlobalParamKey>(key: K): GlobalParamTypeMap[K] | undefined {
      return this.getGlobalParam(key) as GlobalParamTypeMap[K];
   }

   setGlobalParam<keyType extends GlobalParamKey>(
      key: keyType,
      value: GlobalParamTypeMap[keyType]
   ): void {
      // Check if the global parameter already exists in the store
      if (this.globalParamStore[key]) {
         // Update the existing parameter
         this.globalParamStore[key].setGlobalParam(key, value);
      } else {
         // If it doesn’t exist, create a new GlobalParam and add it to the store
         const newGlobalParam = new GlobalParam();
         newGlobalParam.setGlobalParam(key, value);
         this.globalParamStore[key] = newGlobalParam;
      }
   }


}
